"""
装饰器模块

功能：
- API调用日志记录：自动记录接口调用的开始和结束
- 异常处理：捕获和记录接口调用异常
- 性能监控：可扩展添加性能监控功能
- 类型安全：保持原函数的类型签名

主要装饰器：
- @api_call: API调用日志装饰器

特性：
- 自动日志记录：记录接口名称和调用状态
- 异常捕获：记录详细的错误信息
- 类型保持：使用泛型保持原函数类型
- 零侵入：不改变原函数的行为和返回值

使用方式：
    from common.wrapper import api_call

    @api_call
    def eth_chainId(self, url, **data):
        return self._make_rpc_call(url, **data)

日志输出示例：
    INFO: 开始调用接口：eth_chainId
    INFO: 结束调用接口：eth_chainId
    ERROR: 接口调用失败：eth_chainId, 错误: Connection timeout
"""

import functools
from typing import Callable, Any, TypeVar
from loguru import logger

# 泛型类型变量
F = TypeVar('F', bound=Callable[..., Any])


def api_call(func: F) -> F:
    """
    API调用日志记录装饰器

    自动记录API方法的调用开始和结束日志，用于调试和监控。

    Args:
        func: 被装饰的函数

    Returns:
        装饰后的函数，保持原函数的类型签名

    Example:
        >>> @api_call
        ... def eth_chainId(self, url, **data):
        ...     return self._make_rpc_call(url, **data)
        ...
        >>> # 调用时会自动记录日志：
        >>> # INFO: 开始调用接口：eth_chainId
        >>> # INFO: 结束调用接口：eth_chainId
    """

    @functools.wraps(func)
    def inner(*args: Any, **kwargs: Any) -> Any:
        # 输出到控制台的日志级别
        logger.info(f"开始调用接口：{func.__name__}")
        try:
            res = func(*args, **kwargs)
            logger.info(f"结束调用接口：{func.__name__}")
            return res
        except Exception as e:
            logger.error(f"接口调用失败：{func.__name__}, 错误: {e}")
            raise

    return inner  # type: ignore
