"""
断言工具模块

功能：
- HTTP响应断言：状态码、响应头、响应体验证
- 内容断言：字符串包含、不包含检查
- JSON数据断言：结构和字段验证
- RPC响应断言：JSON-RPC格式验证
- 错误处理：详细的断言失败信息

核心类：
- AssertionUtils: 断言工具类

主要断言方法：
1. 响应断言：
   - assert_status_and_nodeid(): 验证HTTP状态码和节点ID
   - 检查响应格式和必要字段

2. 内容断言：
   - assert_contains(): 验证内容包含指定字符串
   - assert_not_contains(): 验证内容不包含指定字符串
   - 支持字符串和JSON数据

3. 结构断言：
   - assert_id_and_version(): 验证RPC响应的ID和版本
   - 检查JSON-RPC标准格式

4. 错误处理：
   - 详细的错误信息记录
   - 失败时的上下文信息
   - 调试友好的错误输出

使用方式：
    from common.assertion_utils import AssertionUtils
    AssertionUtils.assert_status_and_nodeid(response)
    AssertionUtils.assert_contains(response.text, '0x')
"""

import json
from loguru import logger


class AssertionUtils:
    """
    断言工具类
    提供各种断言和验证方法
    """

    @staticmethod
    def assert_status_and_nodeid(response):
        """
        断言响应码和节点id
        :param response: 响应对象
        :return: None
        :raises AssertionError: 当断言失败时抛出异常
        """
        try:
            # 断言响应状态码
            assert response.status_code == 200, f"HTTP状态码不是200，实际为: {response.status_code}"
            
            # 解析响应内容
            response_json = response.json()
            
            # 断言节点ID存在
            assert 'id' in response_json, "响应中缺少 'id' 字段"
            
            logger.debug(f"状态码和节点ID断言通过: status={response.status_code}, id={response_json.get('id')}")
            
        except AssertionError as e:
            logger.error(f"断言失败: {e}")
            if response is not None:
                logger.debug(f"失败响应详情: Status={response.status_code}, Headers={response.headers}, Body={response.text[:500]}...")
            raise e

    @staticmethod
    def assert_contains(content, expected='0x'):
        """
        断言包含，默认参数0x
        :param content: 文本内容
        :param expected: 目标文本（字符串或字符串列表）或预期结果数组（包含字典）
        """
        try:
            # 如果content是字符串，尝试解析为JSON
            if isinstance(content, str):
                try:
                    content = json.loads(content)
                except json.JSONDecodeError:
                    pass  # 保持原始字符串格式
            
            # 将content转换为字符串进行搜索
            content_str = json.dumps(content) if not isinstance(content, str) else content
            
            # 处理不同类型的expected参数
            if isinstance(expected, str):
                # 字符串类型：检查是否包含
                assert expected in content_str, f"内容中不包含期望的字符串 '{expected}'"
                logger.debug(f"字符串包含断言通过: '{expected}' 在内容中找到")
                
            elif isinstance(expected, list):
                # 列表类型：检查每个元素
                for item in expected:
                    if isinstance(item, str):
                        assert item in content_str, f"内容中不包含期望的字符串 '{item}'"
                    elif isinstance(item, dict):
                        # 字典类型：检查键值对
                        for key, value in item.items():
                            if isinstance(content, dict):
                                assert key in content, f"响应中缺少键 '{key}'"
                                if value is not None:
                                    assert content[key] == value, f"键 '{key}' 的值不匹配，期望: {value}, 实际: {content.get(key)}"
                            else:
                                assert f'"{key}"' in content_str, f"内容中不包含键 '{key}'"
                                if value is not None:
                                    assert str(value) in content_str, f"内容中不包含值 '{value}'"
                
                logger.debug(f"列表包含断言通过: 所有期望项都在内容中找到")
                
            elif isinstance(expected, dict):
                # 字典类型：检查键值对
                for key, value in expected.items():
                    if isinstance(content, dict):
                        assert key in content, f"响应中缺少键 '{key}'"
                        if value is not None:
                            assert content[key] == value, f"键 '{key}' 的值不匹配，期望: {value}, 实际: {content.get(key)}"
                    else:
                        assert f'"{key}"' in content_str, f"内容中不包含键 '{key}'"
                        if value is not None:
                            assert str(value) in content_str, f"内容中不包含值 '{value}'"
                
                logger.debug(f"字典包含断言通过: 所有键值对都匹配")
            
            else:
                # 其他类型：转换为字符串检查
                expected_str = str(expected)
                assert expected_str in content_str, f"内容中不包含期望的值 '{expected_str}'"
                logger.debug(f"值包含断言通过: '{expected_str}' 在内容中找到")
                
        except AssertionError as e:
            logger.error(f"包含断言失败: {e}")
            logger.error(f"断言参数 - expected: {expected}, content: {content}")
            raise e
        except Exception as e:
            logger.error(f"assert_contains 执行异常: {type(e).__name__}: {e}")
            logger.error(f"断言参数 - expected: {expected}, content: {content}")
            raise e

    @staticmethod
    def assert_id_and_version(content):
        """
        断言响应id和version
        :param content: 文本内容
        :return:
        """
        try:
            # 如果content是字符串，尝试解析为JSON
            if isinstance(content, str):
                try:
                    content = json.loads(content)
                except json.JSONDecodeError as e:
                    raise AssertionError(f"无法解析JSON内容: {e}")
            
            # 检查是否为字典类型
            if not isinstance(content, dict):
                raise AssertionError(f"内容不是字典类型，实际类型: {type(content).__name__}")
            
            # 断言id字段存在
            assert 'id' in content, "响应中缺少 'id' 字段"
            
            # 断言jsonrpc字段存在且值正确
            assert 'jsonrpc' in content, "响应中缺少 'jsonrpc' 字段"
            assert content['jsonrpc'] == '2.0', f"jsonrpc版本不正确，期望: '2.0', 实际: '{content.get('jsonrpc')}'"
            
            logger.debug(f"ID和版本断言通过: id={content.get('id')}, jsonrpc={content.get('jsonrpc')}")
            
        except AssertionError as e:
            logger.error(f"ID和版本断言失败: {e}")
            logger.error(f"断言参数类型: {type(content).__name__}, 内容: {content}")
            raise e
        except Exception as e:
            logger.error(f"assert_id_and_version 执行异常: {type(e).__name__}: {e}")
            logger.error(f"断言参数类型: {type(content).__name__}, 内容: {content}")
            raise e

    @staticmethod
    def assert_not_contains(content, expected='error'):
        """
        断言响应文本不包含
        :param content: 文本内容
        :param expected: 目标文本
        """
        try:
            # 将content转换为字符串
            if isinstance(content, dict):
                content_str = json.dumps(content)
            elif isinstance(content, str):
                content_str = content
            else:
                content_str = str(content)
            
            # 断言不包含指定内容
            assert expected not in content_str, f"内容中不应包含 '{expected}'，但实际包含了"
            
            logger.debug(f"不包含断言通过: '{expected}' 未在内容中找到")
            
        except AssertionError as e:
            logger.error(f"不包含断言失败: {e}")
            logger.error(f"断言参数 - expected: '{expected}', content 类型: {type(content).__name__}")
            raise e
        except Exception as e:
            logger.error(f"assert_not_contains 执行异常: {type(e).__name__}: {e}")
            logger.error(f"断言参数 - expected: '{expected}', content 类型: {type(content).__name__}")
            raise e


# 创建默认的断言工具实例
default_assertion_utils = AssertionUtils()


# 提供便捷的函数接口，保持向后兼容
def assert_status_and_nodeid(response):
    """断言响应码和节点id的便捷函数"""
    return default_assertion_utils.assert_status_and_nodeid(response)


def assert_contains(content, expected='0x'):
    """断言包含的便捷函数"""
    return default_assertion_utils.assert_contains(content, expected)


def assert_id_and_version(content):
    """断言响应id和version的便捷函数"""
    return default_assertion_utils.assert_id_and_version(content)


def assert_not_contains(content, expected='error'):
    """断言不包含的便捷函数"""
    return default_assertion_utils.assert_not_contains(content, expected)
