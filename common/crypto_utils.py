"""
加密和编码工具模块

功能：
- 交易解码：Base64编码的交易数据解码
- 哈希计算：SHA256等哈希算法
- 编码转换：各种编码格式的转换
- 数据验证：加密数据的格式验证
- 错误处理：加密操作异常的处理

核心类：
- CryptoUtils: 加密和编码工具类

主要功能：
1. 交易处理：
   - tx_decoder(): 交易解码和哈希计算
   - 支持Base64编码的交易数据
   - 自动计算交易哈希值

2. 哈希计算：
   - SHA256哈希计算
   - 支持字节数据和字符串
   - 十六进制输出格式

3. 编码转换：
   - Base64编码解码
   - 十六进制转换
   - 字节数据处理

4. 错误处理：
   - 编码格式验证
   - 解码异常捕获
   - 详细的错误日志

使用方式：
    from common.crypto_utils import CryptoUtils
    tx_hash = CryptoUtils.tx_decoder(encoded_transaction)

适用场景：
- 区块链交易处理
- 数据完整性验证
- 加密数据解析
"""

import base64
import hashlib
from loguru import logger


class CryptoUtils:
    """
    加密和编码工具类
    提供各种加密、解码、哈希功能
    """

    @staticmethod
    def tx_decoder(encoded_tx: str) -> str:
        """
        解码交易并计算哈希值
        :param encoded_tx: base64编码的交易数据
        :return: 交易哈希值
        """
        try:
            decoded_tx = base64.b64decode(encoded_tx)
            tx_hash = hashlib.sha256(decoded_tx).hexdigest()
            logger.debug(f"交易解码成功，哈希值: {tx_hash}")
            return tx_hash
        except Exception as e:
            logger.error(f"交易解码失败: {e}")
            raise e

    @staticmethod
    def calculate_sha256(data: bytes) -> str:
        """
        计算SHA256哈希值
        :param data: 要计算哈希的数据
        :return: 十六进制哈希值
        """
        try:
            hash_value = hashlib.sha256(data).hexdigest()
            return hash_value
        except Exception as e:
            logger.error(f"SHA256计算失败: {e}")
            raise e

    @staticmethod
    def calculate_md5(data: bytes) -> str:
        """
        计算MD5哈希值
        :param data: 要计算哈希的数据
        :return: 十六进制哈希值
        """
        try:
            hash_value = hashlib.md5(data).hexdigest()
            return hash_value
        except Exception as e:
            logger.error(f"MD5计算失败: {e}")
            raise e

    @staticmethod
    def base64_encode(data: bytes) -> str:
        """
        Base64编码
        :param data: 要编码的数据
        :return: Base64编码字符串
        """
        try:
            encoded = base64.b64encode(data).decode('utf-8')
            return encoded
        except Exception as e:
            logger.error(f"Base64编码失败: {e}")
            raise e

    @staticmethod
    def base64_decode(encoded_str: str) -> bytes:
        """
        Base64解码
        :param encoded_str: Base64编码字符串
        :return: 解码后的字节数据
        """
        try:
            decoded = base64.b64decode(encoded_str)
            return decoded
        except Exception as e:
            logger.error(f"Base64解码失败: {e}")
            raise e

    @staticmethod
    def hex_to_bytes(hex_str: str) -> bytes:
        """
        十六进制字符串转字节
        :param hex_str: 十六进制字符串
        :return: 字节数据
        """
        try:
            # 移除0x前缀（如果存在）
            if hex_str.startswith('0x'):
                hex_str = hex_str[2:]
            
            # 确保长度为偶数
            if len(hex_str) % 2 != 0:
                hex_str = '0' + hex_str
            
            return bytes.fromhex(hex_str)
        except Exception as e:
            logger.error(f"十六进制转字节失败: {e}")
            raise e

    @staticmethod
    def bytes_to_hex(data: bytes, prefix: bool = True) -> str:
        """
        字节转十六进制字符串
        :param data: 字节数据
        :param prefix: 是否添加0x前缀
        :return: 十六进制字符串
        """
        try:
            hex_str = data.hex()
            if prefix:
                hex_str = '0x' + hex_str
            return hex_str
        except Exception as e:
            logger.error(f"字节转十六进制失败: {e}")
            raise e


# 创建默认的加密工具实例
default_crypto_utils = CryptoUtils()


# 提供便捷的函数接口，保持向后兼容
def tx_decoder(encoded_tx: str) -> str:
    """解码交易的便捷函数"""
    return default_crypto_utils.tx_decoder(encoded_tx)


def calculate_sha256(data: bytes) -> str:
    """计算SHA256的便捷函数"""
    return default_crypto_utils.calculate_sha256(data)


def calculate_md5(data: bytes) -> str:
    """计算MD5的便捷函数"""
    return default_crypto_utils.calculate_md5(data)


def base64_encode(data: bytes) -> str:
    """Base64编码的便捷函数"""
    return default_crypto_utils.base64_encode(data)


def base64_decode(encoded_str: str) -> bytes:
    """Base64解码的便捷函数"""
    return default_crypto_utils.base64_decode(encoded_str)
