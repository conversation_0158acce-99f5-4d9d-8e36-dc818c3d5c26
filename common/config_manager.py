"""
配置管理器模块
提供配置管理功能的统一接口
"""

from common.enhanced_config_manager import default_enhanced_config_manager
from common.handle_path import CONFIG_DIR, CASE_DIR


class ConfigManagerWrapper:
    """
    配置管理器包装类，提供兼容的接口
    """

    def __init__(self, enhanced_manager):
        self._enhanced_manager = enhanced_manager

    def get_config(self, file_name: str):
        """
        获取配置数据（兼容旧接口）

        Args:
            file_name: 配置文件路径

        Returns:
            配置数据
        """
        return self._enhanced_manager.load_yaml(file_name)

    def load_yaml(self, file_name: str):
        """
        加载YAML文件

        Args:
            file_name: 配置文件路径

        Returns:
            YAML数据
        """
        return self._enhanced_manager.load_yaml(file_name)

    def reload_config(self, file_name=None):
        """重新加载配置"""
        return self._enhanced_manager.reload_config(file_name)

    def clear_cache(self):
        """清空缓存"""
        return self._enhanced_manager.clear_cache()

    def get_case_data(self):
        """
        获取测试用例数据

        Returns:
            测试用例数据字典
        """
        return self._enhanced_manager.load_yaml(CASE_DIR)

    def get_headers(self):
        """
        获取请求头配置

        Returns:
            请求头字典
        """
        config = self._enhanced_manager.load_yaml(CONFIG_DIR)
        return config.get('request_headers', {}).get('headers', {})

    def get_url_config(self, env_name='alphanet'):
        """
        获取指定环境的URL配置

        Args:
            env_name: 环境名称

        Returns:
            URL配置字典
        """
        config = self._enhanced_manager.load_yaml(CONFIG_DIR)
        return config.get('environments', {}).get(env_name, {})


# 创建包装实例
_config_manager_wrapper = ConfigManagerWrapper(default_enhanced_config_manager)


def get_config_manager():
    """
    获取配置管理器实例

    Returns:
        ConfigManagerWrapper: 配置管理器包装实例
    """
    return _config_manager_wrapper
