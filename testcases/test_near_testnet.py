import pytest
from loguru import logger
from apis.near_api import NearApi
from common.utils import Utils


@pytest.mark.near_testnet
class TestNearTestnet(NearApi):

    datas = NearApi.data

    @pytest.fixture(scope="class")
    def get_latest_block_height(self, env):
        latest_block_height = self.get_block_height(env['near_testnet']).json()['result']['header']['height']
        return latest_block_height

    @pytest.mark.parametrize('data', datas['near_testnet']['view_access_key'])
    def test_view_access_key(self, env, data):
        result = self.view_access_key(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['view_access_key_list'])
    def test_view_access_key_list(self, env, data):
        result = self.view_access_key_list(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['single_access_key_changes'])
    def test_single_access_key_changes(self, env, data):
        result = self.single_access_key_changes(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['all_access_key_changes'])
    def test_all_access_key_changes(self, env, data):
        result = self.all_access_key_changes(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['view_account'])
    def test_view_account(self, env, data):
        result = self.view_account(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['account_changes'])
    @pytest.mark.repeat(3)
    def test_account_changes(self, env, data, get_latest_block_height):
        data = Utils.handle_template(data, {'block': get_latest_block_height})
        # 将模板替换结果转换为整数类型，并处理 block_id 是带有 __int__ 键的字典的情况
        if isinstance(data['payload']['params']['block_id'], dict) and '__int__' in data['payload']['params']['block_id']:
            data['payload']['params']['block_id'] = int(data['payload']['params']['block_id']['__int__'])
        result = self.account_changes(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['view_code'])
    def test_view_code(self, env, data):
        result = self.view_code(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['view_state'])
    def test_view_state(self, env, data):
        result = self.view_state(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['data_changes'])
    @pytest.mark.repeat(3)
    def test_data_changes(self, env, data, get_latest_block_height):
        data = Utils.handle_template(data, {'block': get_latest_block_height})
        # 将模板替换结果转换为整数类型，并处理 block_id 是带有 __int__ 键的字典的情况
        if isinstance(data['payload']['params']['block_id'], dict) and '__int__' in data['payload']['params']['block_id']:
            data['payload']['params']['block_id'] = int(data['payload']['params']['block_id']['__int__'])
        result = self.data_changes(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['contract_code_changes'])
    @pytest.mark.repeat(3)
    def test_contract_code_changes(self, env, data, get_latest_block_height):
        data = Utils.handle_template(data, {'block': get_latest_block_height})
        # 将模板替换结果转换为整数类型，并处理 block_id 是带有 __int__ 键的字典的情况
        if isinstance(data['payload']['params']['block_id'], dict) and '__int__' in data['payload']['params']['block_id']:
            data['payload']['params']['block_id'] = int(data['payload']['params']['block_id']['__int__'])
        result = self.contract_code_changes(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['call_function'])
    def test_call_function(self, env, data):
        result = self.call_function(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        # Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['block'])
    def test_block(self, env, data):
        result = self.block(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['changes_in_block'])
    def test_changes_in_block(self, env, data):
        result = self.changes_in_block(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # @pytest.mark.parametrize('data', datas['near_testnet']['chunk'])
    # @pytest.mark.repeat(3)
    # def test_chunk(self, env, data, get_latest_block_height):
    #     data = Utils.handle_template(data, {'block': get_latest_block_height})
    #     # 将模板替换结果转换为整数类型，并处理 block_id 是带有 __int__ 键的字典的情况
    #     if isinstance(data['payload']['params']['block_id'], dict) and '__int__' in data['payload']['params']['block_id']:
    #         data['payload']['params']['block_id'] = int(data['payload']['params']['block_id']['__int__'])
    #     result = self.chunk(env['near_testnet'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_id_and_version(result.json())
    #     Utils.assert_contains(result.json(), data['expected'])
    #     Utils.assert_not_contains(result.json())
    #     logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['gas_price'])
    @pytest.mark.repeat(3)
    def test_gas_price(self, env, data, get_latest_block_height):
        data = Utils.handle_template(data, {'block': get_latest_block_height})
        # 将模板替换结果转换为整数类型，并处理 block_id 是带有 __int__ 键的字典的情况(这里params 是列表)
        if isinstance(data['payload']['params'], list) and len(data['payload']['params']) > 0:
            if isinstance(data['payload']['params'][0], dict) and '__int__' in data['payload']['params'][0]:
                data['payload']['params'][0] = int(data['payload']['params'][0]['__int__'])
        result = self.gas_price(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['genesis_config'])
    def test_genesis_config(self, env, data):
        result = self.genesis_config(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['protocol_config'])
    def test_protocol_config(self, env, data):
        result = self.protocol_config(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['status'])
    def test_status(self, env, data):
        result = self.status(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['network_info'])
    def test_network_info(self, env, data):
        result = self.network_info(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near_testnet']['validators'])
    def test_validators(self, env, data):
        result = self.validators(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')







    @pytest.mark.parametrize('data', datas['near_testnet']['maintenance_windows'])
    def test_maintenance_windows(self, env, data):
        result = self.maintenance_windows(env['near_testnet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())