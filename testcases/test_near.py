import pytest
from loguru import logger
from apis.near_api import NearApi
from common.utils import Utils


@pytest.mark.near
class TestNear(NearApi):

    datas = NearApi.data

    @pytest.fixture(scope="class")
    def get_latest_block_height(self, env):
        latest_block_height = self.get_block_height(env['near']).json()['result']['header']['height']
        return latest_block_height

    @pytest.mark.parametrize('data', datas['near']['view_access_key'])
    def test_view_access_key(self, env, data):
        result = self.view_access_key(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['view_access_key_list'])
    def test_view_access_key_list(self, env, data):
        result = self.view_access_key_list(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['single_access_key_changes'])
    def test_single_access_key_changes(self, env, data):
        result = self.single_access_key_changes(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['all_access_key_changes'])
    def test_all_access_key_changes(self, env, data):
        result = self.all_access_key_changes(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['view_account'])
    def test_view_account(self, env, data):
        result = self.view_account(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['account_changes'])
    @pytest.mark.repeat(3)
    def test_account_changes(self, env, data, get_latest_block_height):
        data = Utils.handle_template(data, {'block': get_latest_block_height})
        # 将模板替换结果转换为整数类型，并处理 block_id 是带有 __int__ 键的字典的情况
        if isinstance(data['payload']['params']['block_id'], dict) and '__int__' in data['payload']['params']['block_id']:
            data['payload']['params']['block_id'] = int(data['payload']['params']['block_id']['__int__'])
        result = self.account_changes(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['view_code'])
    def test_view_code(self, env, data):
        result = self.view_code(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['view_state'])
    def test_view_state(self, env, data):
        result = self.view_state(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['data_changes'])
    @pytest.mark.repeat(3)
    def test_data_changes(self, env, data, get_latest_block_height):
        data = Utils.handle_template(data, {'block': get_latest_block_height})
        # 将模板替换结果转换为整数类型，并处理 block_id 是带有 __int__ 键的字典的情况
        if isinstance(data['payload']['params']['block_id'], dict) and '__int__' in data['payload']['params']['block_id']:
            data['payload']['params']['block_id'] = int(data['payload']['params']['block_id']['__int__'])
        result = self.data_changes(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['contract_code_changes'])
    @pytest.mark.repeat(3)
    def test_contract_code_changes(self, env, data, get_latest_block_height):
        data = Utils.handle_template(data, {'block': get_latest_block_height})
        # 将模板替换结果转换为整数类型，并处理 block_id 是带有 __int__ 键的字典的情况
        if isinstance(data['payload']['params']['block_id'], dict) and '__int__' in data['payload']['params']['block_id']:
            data['payload']['params']['block_id'] = int(data['payload']['params']['block_id']['__int__'])
        result = self.contract_code_changes(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['call_function'])
    def test_call_function(self, env, data):
        result = self.call_function(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        # Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['block'])
    def test_block(self, env, data):
        result = self.block(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['changes_in_block'])
    def test_changes_in_block(self, env, data):
        result = self.changes_in_block(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['chunk'])
    @pytest.mark.repeat(3)
    def test_chunk(self, env, data, get_latest_block_height):
        data = Utils.handle_template(data, {'block': get_latest_block_height})
        # 将模板替换结果转换为整数类型，并处理 block_id 是带有 __int__ 键的字典的情况
        if isinstance(data['payload']['params']['block_id'], dict) and '__int__' in data['payload']['params']['block_id']:
            data['payload']['params']['block_id'] = int(data['payload']['params']['block_id']['__int__'])
        result = self.chunk(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['gas_price'])
    @pytest.mark.repeat(3)
    def test_gas_price(self, env, data, get_latest_block_height):
        data = Utils.handle_template(data, {'block': get_latest_block_height})
        # 将模板替换结果转换为整数类型，并处理 block_id 是带有 __int__ 键的字典的情况(这里params 是列表)
        if isinstance(data['payload']['params'], list) and len(data['payload']['params']) > 0:
            if isinstance(data['payload']['params'][0], dict) and '__int__' in data['payload']['params'][0]:
                data['payload']['params'][0] = int(data['payload']['params'][0]['__int__'])
        result = self.gas_price(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['genesis_config'])
    def test_genesis_config(self, env, data):
        result = self.genesis_config(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['protocol_config'])
    def test_protocol_config(self, env, data):
        result = self.protocol_config(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['status'])
    def test_status(self, env, data):
        result = self.status(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['network_info'])
    def test_network_info(self, env, data):
        result = self.network_info(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['validators'])
    def test_validators(self, env, data):
        result = self.validators(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['near']['tx'])
    # def test_tx(self, env, data):
    #     result = self.tx(env['near'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_id_and_version(result.json())
    #     Utils.assert_contains(result.json(), data['expected'])
    #     Utils.assert_not_contains(result.json())
    #     logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['near']['tx_status'])
    # def test_tx_status(self, env, data):
    #     result = self.tx_status(env['near'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_id_and_version(result.json())
    #     Utils.assert_contains(result.json(), data['expected'])
    #     Utils.assert_not_contains(result.json())
    #     logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['near']['receipt'])
    # def test_receipt(self, env, data):
    #     result = self.receipt(env['near'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_id_and_version(result.json())
    #     Utils.assert_contains(result.json(), data['expected'])
    #     Utils.assert_not_contains(result.json())
    #     logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['near']['maintenance_windows'])
    def test_maintenance_windows(self, env, data):
        result = self.maintenance_windows(env['near'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')