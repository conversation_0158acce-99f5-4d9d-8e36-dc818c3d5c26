import pytest
from loguru import logger
from common.utils import Utils
from apis.beacon_api import BeaconApi


@pytest.mark.beacon
class TestBeacon(BeaconApi):

    datas = BeaconApi.data

    @pytest.mark.parametrize('data', datas['beacon']['genesis'])
    def test_genesis(self, env, data):
        result = self.genesis(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['block_root'])
    def test_block_root(self, env, data):
        result = self.block_root(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['blob_sidecars'])
    def test_blob_sidecars(self, env, data):
        result = self.blob_sidecars(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['block_header'])
    def test_block_header(self, env, data):
        result = self.block_header(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['committees'])
    def test_committees(self, env, data):
        result = self.committees(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['finality_checkpoints'])
    def test_finality_checkpoints(self, env, data):
        result = self.finality_checkpoints(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['fork'])
    def test_fork(self, env, data):
        result = self.fork(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['root'])
    def test_root(self, env, data):
        result = self.root(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['sync_committees'])
    def test_sync_committees(self, env, data):
        result = self.sync_committees(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['validator_balances'])
    def test_validator_balances(self, env, data):
        result = self.validator_balances(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['validators'])
    def test_validators(self, env, data):
        result = self.validators(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['validator_id'])
    def test_validator_id(self, env, data):
        result = self.validator_id(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['block_reward'])
    def test_block_reward(self, env, data):
        result = self.block_reward(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['beaconState_object'])
    def test_beaconState_object(self, env, data):
        result = self.beaconState_object(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['deposit_contract'])
    def test_deposit_contract(self, env, data):
        result = self.deposit_contract(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['spec'])
    def test_spec(self, env, data):
        result = self.spec(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['peer_count'])
    def test_peer_count(self, env, data):
        result = self.peer_count(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['peers'])
    def test_peers(self, env, data):
        result = self.peers(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['syncing'])
    def test_syncing(self, env, data):
        result = self.syncing(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['version'])
    def test_version(self, env, data):
        result = self.version(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['propose'])
    def test_propose(self, env, data):
        result = self.propose(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['block_details'])
    def test_block_details(self, env, data):
        result = self.block_details(env['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

@pytest.mark.beacon
@pytest.mark.archive
class TestBeaconArchive(BeaconApi):

    datas = BeaconApi.data

    @pytest.mark.parametrize('data', datas['beacon']['genesis'])
    def test_genesis(self, env, data):
        result = self.genesis(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['block_root'])
    def test_block_root(self, env, data):
        result = self.block_root(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['blob_sidecars'])
    def test_blob_sidecars(self, env, data):
        result = self.blob_sidecars(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['block_header'])
    def test_block_header(self, env, data):
        result = self.block_header(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['committees'])
    def test_committees(self, env, data):
        result = self.committees(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['finality_checkpoints'])
    def test_finality_checkpoints(self, env, data):
        result = self.finality_checkpoints(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['fork'])
    def test_fork(self, env, data):
        result = self.fork(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['root'])
    def test_root(self, env, data):
        result = self.root(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['sync_committees'])
    def test_sync_committees(self, env, data):
        result = self.sync_committees(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['validator_balances'])
    def test_validator_balances(self, env, data):
        result = self.validator_balances(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['validators'])
    def test_validators(self, env, data):
        result = self.validators(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['validator_id'])
    def test_validator_id(self, env, data):
        result = self.validator_id(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['block_reward'])
    def test_block_reward(self, env, data):
        result = self.block_reward(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['deposit_contract'])
    def test_deposit_contract(self, env, data):
        result = self.deposit_contract(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['spec'])
    def test_spec(self, env, data):
        result = self.spec(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['peer_count'])
    def test_peer_count(self, env, data):
        result = self.peer_count(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['peers'])
    def test_peers(self, env, data):
        result = self.peers(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['syncing'])
    def test_syncing(self, env, data):
        result = self.syncing(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['version'])
    def test_version(self, env, data):
        result = self.version(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['propose'])
    def test_propose(self, env, data):
        result = self.propose(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['beacon']['block_details'])
    def test_block_details(self, env, data):
        result = self.block_details(env['archive_node']['beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')