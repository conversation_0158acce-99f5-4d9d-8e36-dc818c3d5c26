import pytest
from loguru import logger
from common.utils import Utils
from apis.aptos_api import AptosApi


@pytest.mark.aptos
class TestAptos(AptosApi):

    datas = AptosApi.data

    @pytest.fixture(scope="class")
    def get_latest_block(self, env):
        latest_block = self.get_ledger_info(env['aptos'])
        return latest_block.json()['block_height']

    @pytest.fixture(scope="class")
    def get_last_version(self, env):
        latest_block = self.get_ledger_info(env['aptos'])
        return latest_block.json()['ledger_version']

    @pytest.fixture(scope="class")
    def get_last_transaction(self, env):
        latest_block = self.get_transactions(env['aptos'], '/transactions?limit=1')
        return latest_block.json()[0]['hash']

    @pytest.mark.parametrize('data', datas['aptos']['get_account'])
    def test_get_account(self, env, data):
        result = self.get_account(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_account_resources'])
    def test_get_account_resources(self, env, data):
        result = self.get_account_resources(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_account_modules'])
    def test_get_account_modules(self, env, data):
        result = self.get_account_modules(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_account_resource'])
    def test_get_account_resource(self, env, data):
        result = self.get_account_resource(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_account_module'])
    def test_get_account_module(self, env, data):
        result = self.get_account_module(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_blocks_by_height'])
    def test_get_blocks_by_height(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blockheight': get_latest_block})
        result = self.get_blocks_by_height(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_events_by_creation_number'])
    def test_get_events_by_creation_number(self, env, data):
        result = self.get_events_by_creation_number(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_blocks_by_version'])
    def test_get_blocks_by_version(self, get_last_version, env, data):
        data = Utils.handle_template(data, {'lastversion': get_last_version})
        result = self.get_blocks_by_version(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_events_by_event_handel'])
    def test_get_events_by_event_handel(self, env, data):
        result = self.get_events_by_event_handel(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['show_openapi_explorer'])
    def test_show_openapi_explorer(self, env, data):
        result = self.show_openapi_explorer(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['check_basic_node_health'])
    def test_check_basic_node_health(self, env, data):
        result = self.check_basic_node_health(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_ledger_info'])
    def test_get_ledger_info(self, env, data):
        result = self.get_ledger_info(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_transactions'])
    def test_get_transactions(self, env, data):
        result = self.get_transactions(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_transaction_by_hash'])
    def test_get_transaction_by_hash(self, get_last_transaction, env, data):
        data = Utils.handle_template(data, {'lasttransaction': get_last_transaction})
        result = self.get_transaction_by_hash(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['get_account_transactions'])
    def test_get_account_transactions(self, env, data):
        result = self.get_account_transactions(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['aptos']['estimate_gasprice'])
    def test_estimate_gasprice(self, env, data):
        result = self.estimate_gasprice(env['aptos'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')