import pytest
from loguru import logger
from common.utils import Utils
from apis.jsonrpc_api import JsonrpcApi


@pytest.mark.kaia_kairos
@pytest.mark.evm
class TestKaiaKairos(JsonrpcApi):

    datas = JsonrpcApi.data

    @pytest.fixture(scope="class")
    def get_latest_block_hash(self, env):
        latest_block = self.get_block(env['kaia_kairos'], 'latest')
        if latest_block is None or 'hash' not in latest_block:
             pytest.fail("Failed to get latest block or block hash is missing")
        latest_block_hash = latest_block['hash']
        # Assuming hash is already a usable string or hex string
        return latest_block_hash

    @pytest.fixture(scope="class")
    def get_latest_transaction_hash(self, env):
        latest_block = self.get_block(env['kaia_kairos'], 'latest')
        if latest_block is None or not latest_block.get('transactions'):
            pytest.skip("No transactions in the latest block or failed to get block")
        else:
            transactions = latest_block['transactions']
            if not transactions:
                 pytest.skip("Transaction list is empty in the latest block")

            latest_transaction = transactions[0]
            latest_transaction_hash = None
            if isinstance(latest_transaction, dict) and 'hash' in latest_transaction:
                latest_transaction_hash = latest_transaction['hash']
            elif isinstance(latest_transaction, str):
                latest_transaction_hash = latest_transaction
            # Check if it's an object with a 'to_0x_hex' method
            elif hasattr(latest_transaction, 'to_0x_hex') and callable(getattr(latest_transaction, 'to_0x_hex')):
                 latest_transaction_hash = latest_transaction.to_0x_hex()
            elif not isinstance(latest_transaction, (str, dict)):
                 pytest.fail(f"Unexpected transaction format in block: {latest_transaction}")
            else:
                 pytest.fail(f"Could not extract hash from transaction: {latest_transaction}")

            if latest_transaction_hash is None:
                 pytest.fail(f"Failed to extract transaction hash from {latest_transaction}")

            return latest_transaction_hash

    @pytest.fixture(scope="class")
    def get_new_block_filter_hash(self, env):
        new_block_filter_hash = self.get_new_block_filter(env['kaia_kairos']).filter_id
        return new_block_filter_hash

    @pytest.fixture(scope="class")
    def block_offset_factory(self, env):
        """
        工厂fixture，返回一个可以计算特定偏移量区块号的函数
        """
        latest_block = self.get_block(env['kaia_kairos'], 'latest')
        # 将 latest_block_number 转换为整数
        latest_block_number = int(latest_block['number'], 16) if isinstance(latest_block['number'], str) and latest_block['number'].startswith('0x') else int(latest_block['number'])


        def _get_block_with_offset(offset: int):
            """
            根据最新的区块号计算指定偏移量之前的区块号
            """
            if offset <= 0:
                raise ValueError("Offset must be positive")
            calculated_block_number = latest_block_number - offset
            # 可以添加检查，确保 calculated_block_number 不为负
            if calculated_block_number < 0:
                # 或者根据你的业务逻辑返回 0 或抛出错误
                calculated_block_number = 0
            return hex(calculated_block_number)

        return _get_block_with_offset


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_chainId'])
    def test_eth_chainId(self, env, data):
        result = self.eth_chainId(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_syncing'])
    def test_eth_syncing(self, env, data):
        result = self.eth_syncing(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getBlockByNumber'])
    def test_eth_getBlockByNumber(self, env, data):
        result = self.eth_getBlockByNumber(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getBlockByHash'])
    def test_eth_getBlockByHash(self, get_latest_block_hash, env, data):
        data = Utils.handle_template(data, {'blockhash': get_latest_block_hash})
        result = self.eth_getBlockByHash(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_blockNumber'])
    def test_eth_blockNumber(self, env, data):
        result = self.eth_blockNumber(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_gasPrice'])
    def test_eth_gasPrice(self, env, data):
        result = self.eth_gasPrice(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getBalance'])
    def test_eth_getBalance(self, env, data):
        result = self.eth_getBalance(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getTransactionByHash'])
    @pytest.mark.repeat(3)
    def test_eth_getTransactionByHash(self, get_latest_transaction_hash, env, data):
        if get_latest_transaction_hash:
            data = Utils.handle_template(data, {'transactionhash': get_latest_transaction_hash})
            result = self.eth_getTransactionByHash(env['kaia_kairos'], **data['payload'])
            Utils.assert_status_and_nodeid(result)
            Utils.assert_id_and_version(result.json())
            Utils.assert_contains(result.json(), data['expected'])
            Utils.assert_not_contains(result.json())
            logger.info('用例通过！')
        else:
            pytest.skip('区块无交易信息！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getTransactionByBlockHashAndIndex'])
    @pytest.mark.repeat(3)
    def test_eth_getTransactionByBlockHashAndIndex(self, get_latest_block_hash, env, data):
        data = Utils.handle_template(data, {'blockhash': get_latest_block_hash})
        result = self.eth_getTransactionByBlockHashAndIndex(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])

        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getTransactionByBlockNumberAndIndex'])
    @pytest.mark.repeat(3)
    def test_eth_getTransactionByBlockNumberAndIndex(self, env, data):
        result = self.eth_getTransactionByBlockNumberAndIndex(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])

        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getTransactionReceipt'])
    @pytest.mark.repeat(3)
    def test_eth_getTransactionReceipt(self, get_latest_transaction_hash, env, data):
        if get_latest_transaction_hash:
            data = Utils.handle_template(data, {'transactionhash': get_latest_transaction_hash})
            result = self.eth_getTransactionReceipt(env['kaia_kairos'], **data['payload'])
            Utils.assert_status_and_nodeid(result)
            Utils.assert_id_and_version(result.json())
            Utils.assert_contains(result.json(), data['expected'])
            Utils.assert_not_contains(result.json())
            logger.info('用例通过！')
        else:
            pytest.skip('区块无交易信息！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getTransactionCount'])
    def test_eth_getTransactionCount(self, env, data):
        result = self.eth_getTransactionCount(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getBlockTransactionCountByHash'])
    def test_eth_getBlockTransactionCountByHash(self, get_latest_block_hash, env, data):
        data = Utils.handle_template(data, {'blockhash': get_latest_block_hash})
        result = self.eth_getBlockTransactionCountByHash(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getBlockTransactionCountByNumber'])
    def test_eth_getBlockTransactionCountByNumber(self, env, data):
        result = self.eth_getBlockTransactionCountByNumber(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getLogs'])
    @pytest.mark.repeat(3)
    def test_eth_getLogs(self, env, data, block_offset_factory):
        block_minus_5000 = block_offset_factory(offset=4900)
        block_minus_1024 = block_offset_factory(offset=1000)
        data = Utils.handle_template(data, {'blockminus5000': block_minus_5000, 'blockminus1024': block_minus_1024})
        result = self.eth_getLogs(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getCode'])
    # def test_eth_getCode(self, env, data):
    #     result = self.eth_getCode(env['kaia_kairos'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_call'])
    def test_eth_call(self, env, data):
        result = self.eth_call(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getStorageAt'])
    def test_eth_getStorageAt(self, env, data):
        result = self.eth_getStorageAt(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_estimateGas'])
    def test_eth_estimateGas(self, env, data):
        result = self.eth_estimateGas(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_newFilter'])
    def test_eth_newFilter(self, env, data):
        result = self.eth_newFilter(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_newBlockFilter'])
    def test_eth_newBlockFilter(self, env, data):
        result = self.eth_newBlockFilter(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_newPendingTransactionFilter'])
    def test_eth_newPendingTransactionFilter(self, env, data):
        result = self.eth_newPendingTransactionFilter(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getFilterChanges'])
    @pytest.mark.repeat(3)
    def test_eth_getFilterChanges(self, get_new_block_filter_hash, env, data):
        data = Utils.handle_template(data, {'blockfilterhash': get_new_block_filter_hash})
        result = self.eth_getFilterChanges(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])

        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['net_version'])
    def test_net_version(self, env, data):
        result = self.net_version(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['net_listening'])
    def test_net_listening(self, env, data):
        result = self.net_listening(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['net_peerCount'])
    def test_net_peerCount(self, env, data):
        result = self.net_peerCount(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['web3_clientVersion'])
    def test_web3_clientVersion(self, env, data):
        result = self.web3_clientVersion(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['web3_sha3'])
    def test_web3_sha3(self, env, data):
        result = self.web3_sha3(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['txpool_status'])
    def test_txpool_status(self, env, data):
        result = self.txpool_status(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['eth_getBlockReceipts'])
    @pytest.mark.repeat(3)
    def test_eth_getBlockReceipts(self, env, data):
        result = self.eth_getBlockReceipts(env['kaia_kairos'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    def test_websocket(self, get_latest_block_hash, get_latest_transaction_hash, env):
        self.datas = Utils.handle_template(self.datas, {'blockhash': get_latest_block_hash, 'transactionhash': get_latest_transaction_hash})
        ws = Utils.websocket_connection(env['ws']['kaia_kairos'])

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_chainId'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_chainId'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_getBlockByNumber'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_getBlockByNumber'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_getBlockByHash'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_getBlockByHash'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_blockNumber'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_blockNumber'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_gasPrice'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_gasPrice'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_getBalance'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_getBalance'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_getTransactionReceipt'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_getTransactionReceipt'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_getTransactionCount'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_getTransactionCount'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_getBlockTransactionCountByHash'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_getBlockTransactionCountByHash'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_getBlockTransactionCountByNumber'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_getBlockTransactionCountByNumber'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_getLogs'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_getLogs'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_call'][0]['payload'])
        Utils.assert_contains(result)
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_newFilter'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_newFilter'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_newBlockFilter'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_newBlockFilter'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['kaia_kairos']['eth_newPendingTransactionFilter'][0]['payload'])
        Utils.assert_contains(result, self.datas['kaia_kairos']['eth_newPendingTransactionFilter'][0]['expected'])
        Utils.assert_not_contains(result)
        ws.close()
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['disabled_method'])
    def test_disabled_method(self, env, data):
        result = self.disabled_method(env['kaia_kairos'], **data['payload'])
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['kaia_kairos']['batchCall'])
    def test_batchCall(self, env, data):
        result = self.batchCall(env['kaia_kairos'], data['payload'])
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')