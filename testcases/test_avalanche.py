import pytest
from loguru import logger
from common.utils import Utils
from apis.jsonrpc_api import JsonrpcApi


@pytest.mark.avalanche
@pytest.mark.evm
class TestAvalanche(JsonrpcApi):

    datas = JsonrpcApi.data

    @pytest.fixture(scope="class")
    def get_latest_block_hash(self, env):
        latest_block = self.get_block(env['avalanche'], 'latest')
        if latest_block is None or 'hash' not in latest_block:
             pytest.fail("Failed to get latest block or block hash is missing")
        latest_block_hash = latest_block['hash']
        return latest_block_hash

    @pytest.fixture(scope="class")
    def get_latest_transaction_hash(self, env):
        latest_block = self.get_block(env['avalanche'], 'latest')
        if latest_block is None or not latest_block.get('transactions'):
            pytest.skip("No transactions in the latest block or failed to get block")
        else:
            latest_transaction_hash = latest_block['transactions'][0]
            if isinstance(latest_transaction_hash, dict) and 'hash' in latest_transaction_hash:
                 latest_transaction_hash = latest_transaction_hash['hash']
            elif not isinstance(latest_transaction_hash, str):
                 pytest.fail(f"Unexpected transaction format in block: {latest_transaction_hash}")
            return latest_transaction_hash

    @pytest.fixture(scope="class")
    def get_new_block_filter_hash(self, env):
        new_block_filter_hash = self.get_new_block_filter(env['avalanche']).filter_id
        return new_block_filter_hash

    @pytest.fixture(scope="class")
    def block_offset_factory(self, env):
        """
        工厂fixture，返回一个可以计算特定偏移量区块号的函数
        """
        latest_block = self.get_block(env['avalanche'], 'latest')
        # 将 latest_block_number 转换为整数
        latest_block_number = int(latest_block['number'], 16) if isinstance(latest_block['number'], str) and latest_block['number'].startswith('0x') else int(latest_block['number'])


        def _get_block_with_offset(offset: int):
            """
            根据最新的区块号计算指定偏移量之前的区块号
            """
            if offset <= 0:
                raise ValueError("Offset must be positive")
            calculated_block_number = latest_block_number - offset
            # 可以添加检查，确保 calculated_block_number 不为负
            if calculated_block_number < 0:
                # 或者根据你的业务逻辑返回 0 或抛出错误
                calculated_block_number = 0
            return hex(calculated_block_number)

        return _get_block_with_offset
    
    # eth_chainId
    @pytest.mark.parametrize('data', datas['avalanche']['eth_chainId'])
    def test_eth_chainId(self, env, data):
        result = self.eth_chainId(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_syncing
    @pytest.mark.parametrize('data', datas['avalanche']['eth_syncing'])
    def test_eth_syncing(self, env, data):
        result = self.eth_syncing(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        logger.info('用例通过！')

    # eth_getBlockByNumber
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getBlockByNumber'])
    def test_eth_getBlockByNumber(self, env, data):
        result = self.eth_getBlockByNumber(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getBlockByHash
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getBlockByHash'])
    def test_eth_getBlockByHash(self, get_latest_block_hash, env, data):
        data = Utils.handle_template(data, {'blockhash': get_latest_block_hash})
        result = self.eth_getBlockByHash(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_blockNumber
    @pytest.mark.parametrize('data', datas['avalanche']['eth_blockNumber'])
    def test_eth_blockNumber(self, env, data):
        result = self.eth_blockNumber(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_gasPrice
    @pytest.mark.parametrize('data', datas['avalanche']['eth_gasPrice'])
    def test_eth_gasPrice(self, env, data):
        result = self.eth_gasPrice(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getBalance
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getBalance'])
    def test_eth_getBalance(self, env, data):
        result = self.eth_getBalance(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getTransactionByHash
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getTransactionByHash'])
    def test_eth_getTransactionByHash(self, get_latest_transaction_hash, env, data):
        data = Utils.handle_template(data, {'transactionhash': get_latest_transaction_hash})
        result = self.eth_getTransactionByHash(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getTransactionByBlockHashAndIndex
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getTransactionByBlockHashAndIndex'])
    def test_eth_getTransactionByBlockHashAndIndex(self, get_latest_block_hash, env, data):
        data = Utils.handle_template(data, {'blockhash': get_latest_block_hash})
        result = self.eth_getTransactionByBlockHashAndIndex(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getTransactionByBlockNumberAndIndex
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getTransactionByBlockNumberAndIndex'])
    def test_eth_getTransactionByBlockNumberAndIndex(self, env, data):
        result = self.eth_getTransactionByBlockNumberAndIndex(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getTransactionReceipt
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getTransactionReceipt'])
    def test_eth_getTransactionReceipt(self, get_latest_transaction_hash, env, data):
        data = Utils.handle_template(data, {'transactionhash': get_latest_transaction_hash})
        result = self.eth_getTransactionReceipt(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getTransactionCount
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getTransactionCount'])
    def test_eth_getTransactionCount(self, env, data):
        result = self.eth_getTransactionCount(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getBlockTransactionCountByHash
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getBlockTransactionCountByHash'])
    def test_eth_getBlockTransactionCountByHash(self, get_latest_block_hash, env, data):
        data = Utils.handle_template(data, {'blockhash': get_latest_block_hash})
        result = self.eth_getBlockTransactionCountByHash(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getBlockTransactionCountByNumber
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getBlockTransactionCountByNumber'])
    def test_eth_getBlockTransactionCountByNumber(self, env, data):
        result = self.eth_getBlockTransactionCountByNumber(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getLogs
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getLogs'])
    @pytest.mark.repeat(3)
    def test_eth_getLogs(self, env, data, block_offset_factory):
        block_minus_5000 = block_offset_factory(offset=4900)
        block_minus_1024 = block_offset_factory(offset=1000)
        data = Utils.handle_template(data, {'blockminus5000': block_minus_5000, 'blockminus1024': block_minus_1024})
        result = self.eth_getLogs(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        print(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getCode
    # @pytest.mark.parametrize('data', datas['avalanche']['eth_getCode'])
    # def test_eth_getCode(self, env, data):
    #     result = self.eth_getCode(env['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # eth_call
    @pytest.mark.parametrize('data', datas['avalanche']['eth_call'])
    def test_eth_call(self, env, data):
        result = self.eth_call(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json())
        logger.info('用例通过！')

    # eth_getStorageAt
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getStorageAt'])
    def test_eth_getStorageAt(self, env, data):
        result = self.eth_getStorageAt(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_estimateGas
    @pytest.mark.parametrize('data', datas['avalanche']['eth_estimateGas'])
    def test_eth_estimateGas(self, env, data):
        result = self.eth_estimateGas(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_newFilter
    @pytest.mark.parametrize('data', datas['avalanche']['eth_newFilter'])
    def test_eth_newFilter(self, env, data):
        result = self.eth_newFilter(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_newBlockFilter
    @pytest.mark.parametrize('data', datas['avalanche']['eth_newBlockFilter'])
    def test_eth_newBlockFilter(self, env, data):
        result = self.eth_newBlockFilter(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_newPendingTransactionFilter
    # @pytest.mark.parametrize('data', datas['avalanche']['eth_newPendingTransactionFilter'])
    # def test_eth_newPendingTransactionFilter(self, env, data):
    #     result = self.eth_newPendingTransactionFilter(env['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # eth_getFilterChanges
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getFilterChanges'])
    @pytest.mark.repeat(3)
    def test_eth_getFilterChanges(self, get_new_block_filter_hash, env, data):
        data = Utils.handle_template(data, {'blockfilterhash': get_new_block_filter_hash})
        result = self.eth_getFilterChanges(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])

        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # net_version
    # @pytest.mark.parametrize('data', datas['avalanche']['net_version'])
    # def test_net_version(self, env, data):
    #     result = self.net_version(env['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # net_listening
    # @pytest.mark.parametrize('data', datas['avalanche']['net_listening'])
    # def test_net_listening(self, env, data):
    #     result = self.net_listening(env['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     logger.info('用例通过！')
    #
    # net_peerCount
    # @pytest.mark.parametrize('data', datas['avalanche']['net_peerCount'])
    # def test_net_peerCount(self, env, data):
    #     result = self.net_peerCount(env['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # web3_clientVersion
    @pytest.mark.parametrize('data', datas['avalanche']['web3_clientVersion'])
    def test_web3_clientVersion(self, env, data):
        result = self.web3_clientVersion(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # web3_sha3
    @pytest.mark.parametrize('data', datas['avalanche']['web3_sha3'])
    def test_web3_sha3(self, env, data):
        result = self.web3_sha3(env['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # txpool_status
    # @pytest.mark.parametrize('data', datas['avalanche']['txpool_status'])
    # def test_txpool_status(self, env, data):
    #     result = self.txpool_status(env['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # eth_getBlockReceipts
    # @pytest.mark.parametrize('data', datas['avalanche']['eth_getBlockReceipts'])
    # def test_eth_getBlockReceipts(self, env, data):
    #     result = self.txpool_status(env['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # websocket
    def test_websocket(self, get_latest_block_hash, get_latest_transaction_hash, env):
        self.datas = Utils.handle_template(self.datas, {'blockhash': get_latest_block_hash, 'transactionhash': get_latest_transaction_hash})
        ws = Utils.websocket_connection(env['ws']['avalanche'])

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_chainId'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_chainId'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getBlockByNumber'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getBlockByNumber'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getBlockByHash'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getBlockByHash'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_blockNumber'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_blockNumber'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_gasPrice'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_gasPrice'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getBalance'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getBalance'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getTransactionByHash'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getTransactionByHash'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getTransactionByBlockHashAndIndex'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getTransactionByBlockHashAndIndex'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getTransactionByBlockNumberAndIndex'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getTransactionByBlockNumberAndIndex'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getTransactionReceipt'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getTransactionReceipt'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getTransactionCount'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getTransactionCount'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getBlockTransactionCountByHash'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getBlockTransactionCountByHash'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getBlockTransactionCountByNumber'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getBlockTransactionCountByNumber'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getLogs'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getLogs'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_call'][0]['payload'])
        Utils.assert_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_newFilter'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_newFilter'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_newBlockFilter'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_newBlockFilter'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_newPendingTransactionFilter'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_newPendingTransactionFilter'][0]['expected'])
        Utils.assert_not_contains(result)
        ws.close()
        logger.info('用例通过！')

    # disabled_method
    @pytest.mark.parametrize('data', datas['avalanche']['disabled_method'])
    def test_disabled_method(self, env, data):
        result = self.disabled_method(env['avalanche'], **data['payload'])
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    # batchCall
    @pytest.mark.parametrize('data', datas['avalanche']['batchCall'])
    def test_batchCall(self, env, data):
        result = self.batchCall(env['avalanche'], data['payload'])
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

@pytest.mark.avalanche
@pytest.mark.evm
@pytest.mark.archive
class TestAvalancheArchive(JsonrpcApi):

    datas = JsonrpcApi.data

    @pytest.fixture(scope="class")
    def get_latest_block_hash(self, env):
        latest_block_hash = self.get_block(env['avalanche'], 'latest')['hash']
        return latest_block_hash

    @pytest.fixture(scope="class")
    def get_latest_transaction_hash(self, env):
        latest_block = self.get_block(env['avalanche'], 'latest')
        if latest_block is None or not latest_block.get('transactions'):
            pytest.skip("No transactions in the latest block or failed to get block")
        else:
            latest_transaction_hash = latest_block['transactions'][0]
            # 检查交易对象是否为字典，如果是，则尝试获取 'hash' 键
            if isinstance(latest_transaction_hash, dict) and 'hash' in latest_transaction_hash:
                latest_transaction_hash = latest_transaction_hash['hash']
            # 如果不是字符串类型，则表示交易格式不符合预期，抛出错误
            elif not isinstance(latest_transaction_hash, str):
                pytest.fail(f"Unexpected transaction format in block: {latest_transaction_hash}")
            return latest_transaction_hash

    @pytest.fixture(scope="class")
    def get_new_block_filter_hash(self, env):
        new_block_filter_hash = self.get_new_block_filter(env['avalanche']).filter_id
        return new_block_filter_hash
    
    @pytest.fixture(scope="class")
    def block_offset_factory(self, env):
        """
        工厂fixture，返回一个可以计算特定偏移量区块号的函数
        """
        latest_block = self.get_block(env['avalanche'], 'latest')
        # 将 latest_block_number 转换为整数
        latest_block_number = int(latest_block['number'], 16) if isinstance(latest_block['number'], str) and latest_block['number'].startswith('0x') else int(latest_block['number'])


        def _get_block_with_offset(offset: int):
            """
            根据最新的区块号计算指定偏移量之前的区块号
            """
            if offset <= 0:
                raise ValueError("Offset must be positive")
            calculated_block_number = latest_block_number - offset
            # 可以添加检查，确保 calculated_block_number 不为负
            if calculated_block_number < 0:
                # 或者根据你的业务逻辑返回 0 或抛出错误
                calculated_block_number = 0
            return hex(calculated_block_number)

        return _get_block_with_offset

    # eth_chainId
    @pytest.mark.parametrize('data', datas['avalanche']['eth_chainId'])
    def test_eth_chainId(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_chainId(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')
        
    # eth_syncing
    @pytest.mark.parametrize('data', datas['avalanche']['eth_syncing'])
    def test_eth_syncing(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_syncing(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        logger.info('用例通过！')

    # eth_getBlockByNumber
    @pytest.mark.parametrize('data', datas['avalanche']['archive']['eth_getBlockByNumber'])
    def test_eth_getBlockByNumber(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_getBlockByNumber(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getBlockByHash
    @pytest.mark.parametrize('data', datas['avalanche']['archive']['eth_getBlockByHash'])
    def test_eth_getBlockByHash(self, env, data, get_latest_block_hash):
        logger.info('开始请求存档节点')
        data = Utils.handle_template(data, {'blockhash': get_latest_block_hash})
        result = self.eth_getBlockByHash(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_blockNumber
    @pytest.mark.parametrize('data', datas['avalanche']['eth_blockNumber'])
    def test_eth_blockNumber(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_blockNumber(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_gasPrice
    @pytest.mark.parametrize('data', datas['avalanche']['eth_gasPrice'])
    def test_eth_gasPrice(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_gasPrice(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getBalance
    @pytest.mark.parametrize('data', datas['avalanche']['archive']['eth_getBalance'])
    def test_eth_getBalance(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_getBalance(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getTransactionByHash
    @pytest.mark.parametrize('data', datas['avalanche']['archive']['eth_getTransactionByHash'])
    def test_eth_getTransactionByHash(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_getTransactionByHash(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getTransactionByBlockHashAndIndex
    @pytest.mark.parametrize('data', datas['avalanche']['archive']['eth_getTransactionByBlockHashAndIndex'])
    def test_eth_getTransactionByBlockHashAndIndex(self, get_latest_block_hash, env, data):
        logger.info('开始请求存档节点')
        data = Utils.handle_template(data, {'blockhash': get_latest_block_hash})
        result = self.eth_getTransactionByBlockHashAndIndex(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getTransactionByBlockNumberAndIndex
    @pytest.mark.parametrize('data', datas['avalanche']['archive']['eth_getTransactionByBlockNumberAndIndex'])
    def test_eth_getTransactionByBlockNumberAndIndex(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_getTransactionByBlockNumberAndIndex(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getTransactionReceipt
    @pytest.mark.parametrize('data', datas['avalanche']['archive']['eth_getTransactionReceipt'])
    def test_eth_getTransactionReceipt(self, get_latest_transaction_hash, env, data):
        logger.info('开始请求存档节点')
        data = Utils.handle_template(data, {'transactionhash': get_latest_transaction_hash})
        result = self.eth_getTransactionReceipt(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getTransactionCount
    @pytest.mark.parametrize('data', datas['avalanche']['archive']['eth_getTransactionCount'])
    def test_eth_getTransactionCount(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_getTransactionCount(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getBlockTransactionCountByHash
    @pytest.mark.parametrize('data', datas['avalanche']['archive']['eth_getBlockTransactionCountByHash'])
    def test_eth_getBlockTransactionCountByHash(self, get_latest_block_hash, env, data):
        logger.info('开始请求存档节点')
        data = Utils.handle_template(data, {'blockhash': get_latest_block_hash})
        result = self.eth_getBlockTransactionCountByHash(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getBlockTransactionCountByNumber
    @pytest.mark.parametrize('data', datas['avalanche']['archive']['eth_getBlockTransactionCountByNumber'])
    def test_eth_getBlockTransactionCountByNumber(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_getBlockTransactionCountByNumber(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getLogs
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getLogs'])
    def test_eth_getLogs(self, env, data, block_offset_factory):
        block_minus_5000 = block_offset_factory(offset=4900)
        block_minus_1024 = block_offset_factory(offset=1000)
        data = Utils.handle_template(data, {'blockminus5000': block_minus_5000, 'blockminus1024': block_minus_1024})
        logger.info('开始请求存档节点')
        result = self.eth_getLogs(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getCode
    # @pytest.mark.parametrize('data', datas['avalanche']['eth_getCode'])
    # def test_eth_getCode(self, env, data):
    #     result = self.eth_getCode(env['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('开始请求存档节点')
    #     result = self.eth_getCode(env['archive_node']['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # eth_call
    @pytest.mark.parametrize('data', datas['avalanche']['eth_call'])
    def test_eth_call(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_call(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json())
        logger.info('用例通过！')

    # eth_getStorageAt
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getStorageAt'])
    def test_eth_getStorageAt(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_getStorageAt(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_estimateGas
    @pytest.mark.parametrize('data', datas['avalanche']['eth_estimateGas'])
    def test_eth_estimateGas(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_estimateGas(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_newFilter
    @pytest.mark.parametrize('data', datas['avalanche']['eth_newFilter'])
    def test_eth_newFilter(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_newFilter(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_newBlockFilter
    @pytest.mark.parametrize('data', datas['avalanche']['eth_newBlockFilter'])
    def test_eth_newBlockFilter(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_newBlockFilter(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_newPendingTransactionFilter
    @pytest.mark.parametrize('data', datas['avalanche']['eth_newPendingTransactionFilter'])
    def test_eth_newPendingTransactionFilter(self, env, data):
        logger.info('开始请求存档节点')
        result = self.eth_newPendingTransactionFilter(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # eth_getFilterChanges
    @pytest.mark.parametrize('data', datas['avalanche']['eth_getFilterChanges'])
    @pytest.mark.repeat(3)
    def test_eth_getFilterChanges(self, get_new_block_filter_hash, env, data):
        logger.info('开始请求存档节点')
        data['payload']['params'] = [get_new_block_filter_hash]
        result = self.eth_getFilterChanges(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # net_version
    # @pytest.mark.parametrize('data', datas['avalanche']['net_version'])
    # def test_net_version(self, env, data):
    #     logger.info('开始请求存档节点')
    #     result = self.net_version(env['archive_node']['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # net_listening
    # @pytest.mark.parametrize('data', datas['avalanche']['net_listening'])
    # def test_net_listening(self, env, data):
    #     logger.info('开始请求存档节点')
    #     result = self.net_listening(env['archive_node']['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     logger.info('用例通过！')
    #
    # net_peerCount
    # @pytest.mark.parametrize('data', datas['avalanche']['net_peerCount'])
    # def test_net_peerCount(self, env, data):
    #     logger.info('开始请求存档节点')
    #     result = self.net_peerCount(env['archive_node']['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # web3_clientVersion
    @pytest.mark.parametrize('data', datas['avalanche']['web3_clientVersion'])
    def test_web3_clientVersion(self, env, data):
        logger.info('开始请求存档节点')
        result = self.web3_clientVersion(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['avalanche']['web3_sha3'])
    def test_web3_sha3(self, env, data):
        logger.info('开始请求存档节点')
        result = self.web3_sha3(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # txpool_status
    # @pytest.mark.parametrize('data', datas['avalanche']['txpool_status'])
    # def test_txpool_status(self, env, data):
    #     logger.info('开始请求存档节点')
    #     result = self.txpool_status(env['archive_node']['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # eth_getBlockReceipts
    # @pytest.mark.parametrize('data', datas['avalanche']['eth_getBlockReceipts'])
    # def test_eth_getBlockReceipts(self, env, data):
    #     result = self.txpool_status(env['avalanche'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # disabled_method
    @pytest.mark.parametrize('data', datas['avalanche']['disabled_method'])
    def test_disabled_method(self, env, data):
        logger.info('开始请求存档节点')
        result = self.disabled_method(env['archive_node']['avalanche'], **data['payload'])
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['avalanche']['batchCall'])
    def test_batchCall(self, env, data):
        result = self.batchCall(env['archive_node']['avalanche'], data['payload'])
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    def test_websocket(self, get_latest_block_hash, get_latest_transaction_hash, env):
        self.datas = Utils.handle_template(self.datas, {'blockhash': get_latest_block_hash, 'transactionhash': get_latest_transaction_hash})
        ws = Utils.websocket_connection(env['archive_node']['ws']['avalanche'])

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_chainId'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_chainId'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getBlockByNumber'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getBlockByNumber'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getBlockByHash'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getBlockByHash'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_blockNumber'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_blockNumber'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_gasPrice'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_gasPrice'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getBalance'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getBalance'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getTransactionByHash'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getTransactionByHash'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getTransactionByBlockHashAndIndex'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getTransactionByBlockHashAndIndex'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getTransactionByBlockNumberAndIndex'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getTransactionByBlockNumberAndIndex'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getTransactionReceipt'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getTransactionReceipt'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getTransactionCount'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getTransactionCount'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getBlockTransactionCountByHash'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getBlockTransactionCountByHash'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getBlockTransactionCountByNumber'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getBlockTransactionCountByNumber'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_getLogs'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_getLogs'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_call'][0]['payload'])
        Utils.assert_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_newFilter'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_newFilter'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_newBlockFilter'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_newBlockFilter'][0]['expected'])
        Utils.assert_not_contains(result)

        result = Utils.send_websocket(ws, self.datas['avalanche']['eth_newPendingTransactionFilter'][0]['payload'])
        Utils.assert_contains(result, self.datas['avalanche']['eth_newPendingTransactionFilter'][0]['expected'])
        Utils.assert_not_contains(result)
        ws.close()
        logger.info('用例通过！')