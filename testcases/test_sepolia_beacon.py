import pytest
from loguru import logger
from common.utils import Utils
from apis.beacon_api import BeaconApi


@pytest.mark.sepolia_beacon
class TestSepoliaBeacon(BeaconApi):

    datas = BeaconApi.data

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['genesis'])
    def test_genesis(self, env, data):
        result = self.genesis(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['block_root'])
    def test_block_root(self, env, data):
        result = self.block_root(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['blob_sidecars'])
    def test_blob_sidecars(self, env, data):
        result = self.blob_sidecars(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['block_header'])
    def test_block_header(self, env, data):
        result = self.block_header(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['committees'])
    def test_committees(self, env, data):
        result = self.committees(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['finality_checkpoints'])
    def test_finality_checkpoints(self, env, data):
        result = self.finality_checkpoints(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['fork'])
    def test_fork(self, env, data):
        result = self.fork(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['root'])
    def test_root(self, env, data):
        result = self.root(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['sync_committees'])
    def test_sync_committees(self, env, data):
        result = self.sync_committees(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['validator_balances'])
    def test_validator_balances(self, env, data):
        result = self.validator_balances(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['validators'])
    def test_validators(self, env, data):
        result = self.validators(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['validator_id'])
    def test_validator_id(self, env, data):
        result = self.validator_id(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['block_reward'])
    def test_block_reward(self, env, data):
        result = self.block_reward(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['beaconState_object'])
    def test_beaconState_object(self, env, data):
        result = self.beaconState_object(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['deposit_contract'])
    def test_deposit_contract(self, env, data):
        result = self.deposit_contract(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['spec'])
    def test_spec(self, env, data):
        result = self.spec(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['peer_count'])
    def test_peer_count(self, env, data):
        result = self.peer_count(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['peers'])
    def test_peers(self, env, data):
        result = self.peers(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['syncing'])
    def test_syncing(self, env, data):
        result = self.syncing(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['version'])
    def test_version(self, env, data):
        result = self.version(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['propose'])
    def test_propose(self, env, data):
        result = self.propose(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['block_details'])
    def test_block_details(self, env, data):
        result = self.block_details(env['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

@pytest.mark.sepolia_beacon
@pytest.mark.archive
class TestSepoliaBeaconArchive(BeaconApi):

    datas = BeaconApi.data

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['genesis'])
    def test_genesis(self, env, data):
        result = self.genesis(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['block_root'])
    def test_block_root(self, env, data):
        result = self.block_root(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['blob_sidecars'])
    def test_blob_sidecars(self, env, data):
        result = self.blob_sidecars(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['block_header'])
    def test_block_header(self, env, data):
        result = self.block_header(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['committees'])
    def test_committees(self, env, data):
        result = self.committees(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['finality_checkpoints'])
    def test_finality_checkpoints(self, env, data):
        result = self.finality_checkpoints(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['fork'])
    def test_fork(self, env, data):
        result = self.fork(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['root'])
    def test_root(self, env, data):
        result = self.root(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['sync_committees'])
    def test_sync_committees(self, env, data):
        result = self.sync_committees(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['validator_balances'])
    def test_validator_balances(self, env, data):
        result = self.validator_balances(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['validators'])
    def test_validators(self, env, data):
        result = self.validators(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['validator_id'])
    def test_validator_id(self, env, data):
        result = self.validator_id(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['block_reward'])
    def test_block_reward(self, env, data):
        result = self.block_reward(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['deposit_contract'])
    def test_deposit_contract(self, env, data):
        result = self.deposit_contract(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['spec'])
    def test_spec(self, env, data):
        result = self.spec(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['peer_count'])
    def test_peer_count(self, env, data):
        result = self.peer_count(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['peers'])
    def test_peers(self, env, data):
        result = self.peers(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['syncing'])
    def test_syncing(self, env, data):
        result = self.syncing(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['version'])
    def test_version(self, env, data):
        result = self.version(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['propose'])
    def test_propose(self, env, data):
        result = self.propose(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sepolia_beacon']['block_details'])
    def test_block_details(self, env, data):
        result = self.block_details(env['archive_node']['sepolia_beacon'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')