import pytest
from loguru import logger
from common.utils import Utils
from apis.cosmos_api import CosmosApi


@pytest.mark.zeta
class TestZeta(CosmosApi):

    datas = CosmosApi.data

    @pytest.fixture(scope="class")
    def get_latest_block(self, env):
        latest_block = self.latest_block(env['zeta_lcd'], '/cosmos/base/tendermint/v1beta1/blocks/latest')
        return latest_block.json()['block']['header']['height']

    @pytest.fixture(scope="class")
    def get_latest_block_hash(self, env):
        latest_block = self.latest_block(env['zeta_lcd'], '/cosmos/base/tendermint/v1beta1/blocks/latest')
        return latest_block.json()['block_id']['hash']
    
    @pytest.fixture(scope="class")
    def get_latest_tx_hash(self, env):
        latest_block = self.latest_block(env['zeta_lcd'], '/cosmos/base/tendermint/v1beta1/blocks/latest')
        encoded_tx = latest_block.json()['block']['data']['txs'][0]
        latest_tx_hash = Utils.tx_decoder(encoded_tx)
        return latest_tx_hash


    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['accounts'])
    # def test_accounts(self, env, data):
    #     result = self.accounts(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['account_details'])
    def test_account_details(self, env, data):
        result = self.account_details(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['params'])
    def test_params(self, env, data):
        result = self.params(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

 
    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['grants'])
    # def test_grants(self, env, data):
    #     result = self.grants(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['grantee'])
    # def test_grantee(self, env, data):
    #     result = self.grantee(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')
   
    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['granter'])
    # def test_granter(self, env, data):
    #     result = self.granter(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['balances'])
    def test_balances(self, env, data):
        result = self.balances(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['balance'])
    def test_balance(self, env, data):
        result = self.balance(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['denoms_metadata'])
    def test_denoms_metadata(self, env, data):
        result = self.denoms_metadata(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['denom_metadata'])
    def test_denom_metadata(self, env, data):
        result = self.denom_metadata(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['bank_params'])
    def test_bank_params(self, env, data):
        result = self.bank_params(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['spendable_balances'])
    def test_spendable_balances(self, env, data):
        result = self.spendable_balances(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['spendable_balances'])
    def test_spendable_balances(self, env, data):
        result = self.spendable_balances(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['total_supply'])
    def test_total_supply(self, env, data):
        result = self.total_supply(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['supply'])
    def test_supply(self, env, data):
        result = self.supply(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['block_by_height'])
    def test_block_by_height(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.block_by_height(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['latest_block'])
    def test_latest_block(self, env, data):
        result = self.latest_block(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['node_info'])
    def test_node_info(self, env, data):
        result = self.node_info(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        # Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['node_syncing'])
    def test_node_syncing(self, env, data):
        result = self.node_syncing(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['validatorsets'])
    def test_validatorsets(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.validatorsets(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['latest_validatorsets'])
    def test_latest_validatorsets(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.latest_validatorsets(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['community_pool'])
    def test_community_pool(self, env, data):
        result = self.community_pool(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['delegator_rewards'])
    def test_delegator_rewards(self, env, data):
        result = self.delegator_rewards(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['delegation_rewards'])
    def test_delegation_rewards(self, env, data):
        result = self.delegation_rewards(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['validators_of_delegator'])
    def test_validators_of_delegator(self, env, data):
        result = self.validators_of_delegator(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['delegator_withdraw_address'])
    # def test_delegator_withdraw_address(self, env, data):
    #     result = self.delegator_withdraw_address(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['distribution_params'])
    def test_distribution_params(self, env, data):
        result = self.distribution_params(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['validators_commisson'])
    def test_validators_commisson(self, env, data):
        result = self.validators_commisson(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['validators_rewards'])
    def test_validators_rewards(self, env, data):
        result = self.validators_rewards(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['validator_slashes'])
    def test_validator_slashes(self, env, data):
        result = self.validator_slashes(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['evidence'])
    # def test_evidence(self, env, data):
    #     result = self.evidence(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     Utils.assert_not_contains(result.json())
    #     logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['evidence'])
    # def test_granted_fee(self, env, data):
    #     result = self.granted_fee(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['address_grants'])
    # def test_address_grants(self, env, data):
    #     result = self.address_grants(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')
  
    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['grants_give_address'])
    # def test_grants_give_address(self, env, data):
    #     result = self.grants_give_address(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['gov_params'])
    def test_gov_params(self, env, data):
        result = self.gov_params(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['proposals'])
    def test_proposals(self, env, data):
        result = self.proposals(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['proposal_id'])
    def test_proposal_id(self, env, data):
        result = self.proposal_id(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['proposal_deposits'])
    def test_proposal_deposits(self, env, data):
        result = self.proposal_deposits(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['proposal_vote'])
    def test_proposal_vote(self, env, data):
        result = self.proposal_vote(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['votes_of_proposal'])
    def test_votes_of_proposal(self, env, data):
        result = self.votes_of_proposal(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['slashing_params'])
    def test_slashing_params(self, env, data):
        result = self.slashing_params(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['validators_signing_info'])
    def test_validators_signing_info(self, env, data):
        result = self.validators_signing_info(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['delegator_delegations'])
    def test_delegator_delegations(self, env, data):
        result = self.delegator_delegations(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['address_unbonding_redeleations'])
    def test_address_unbonding_redeleations(self, env, data):
        result = self.address_unbonding_redeleations(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['address_validators_info'])
    def test_address_validators_info(self, env, data):
        result = self.address_validators_info(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['staking_params'])
    def test_staking_params(self, env, data):
        result = self.staking_params(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['pool_info'])
    def test_pool_info(self, env, data):
        result = self.pool_info(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['validators_status'])
    def test_validators_status(self, env, data):
        result = self.validators_status(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['delegate_info'])
    def test_delegate_info(self, env, data):
        result = self.delegate_info(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['delegate_info'])
    def test_delegate_info(self, env, data):
        result = self.delegate_info(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # 
    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['unbonding_delegation'])
    # def test_unbonding_delegation(self, env, data):
    #     result = self.unbonding_delegation(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['validator_unbonding_delegations'])
    def test_validator_unbonding_delegations(self, env, data):
        result = self.validator_unbonding_delegations(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['block_decoded_txs'])
    def test_block_decoded_txs(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.block_decoded_txs(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['tx_by_hash'])
    def test_tx_by_hash(self, env, data, get_latest_tx_hash):
        data = Utils.handle_template(data, {'latesttxhash': get_latest_tx_hash})
        result = self.tx_by_hash(env['zeta_lcd'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['applied_plan'])
    # def test_applied_plan(self, env, data):
    #     result = self.applied_plan(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     Utils.assert_not_contains(result.json())
    #     logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['current_plan'])
    # def test_current_plan(self, env, data):
    #     result = self.current_plan(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     Utils.assert_not_contains(result.json())
    #     logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['module_versions'])
    # def test_module_versions(self, env, data):
    #     result = self.module_versions(env['zeta_lcd'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     Utils.assert_not_contains(result.json())
    #     logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['abci_info'])
    def test_abci_info_rest(self, env, data):
        result = self.abci_info_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['abci_info'])
    def test_abci_info_jsonrpc(self, env, data):
        result = self.abci_info_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['block'])
    # def test_block_rest(self, env, data):
    #     result = self.block_rest(env['zeta_rpc'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')
 
    # @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['block'])
    # def test_block_jsonrpc(self, env, data):
    #     result = self.block_jsonrpc(env['zeta_rpc'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_id_and_version(result.json())
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')


    @pytest.mark.skip('无法获取最新hash，暂时跳过')
    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['block_by_hash'])
    def test_block_by_hash_rest(self, get_latest_block_hash, env, data):
        data = Utils.handle_template(data, {'blockhash': get_latest_block_hash})
        result = self.block_by_hash_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['block_by_hash'])
    def test_block_by_hash_jsonrpc(self, get_latest_block_hash, env, data):
        data = Utils.handle_template(data, {'blockhash': get_latest_block_hash})
        result = self.block_by_hash_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    
    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['block_results'])
    def test_block_results_rest(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.block_results_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    
    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['block_results'])
    def test_block_results_jsonrpc(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.block_results_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    
    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['blockchain'])
    def test_blockchain_rest(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.blockchain_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    
    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['blockchain'])
    def test_blockchain_jsonrpc(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.blockchain_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    
    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['commit'])
    def test_commit_rest(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.commit_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    
    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['commit'])
    def test_commit_jsonrpc(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.commit_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

# 
#     @pytest.mark.parametrize('data', datas['zeta']['rest_api']['consensus_params'])
#     def test_consensus_params_rest(self, get_latest_block, env, data):
#         data = Utils.handle_template(data, {'blocknumber': get_latest_block})
#         result = self.consensus_params_rest(env['zeta_rpc'], data['payload'])
#         Utils.assert_status_and_nodeid(result)
#         Utils.assert_contains(result.json(), data['expected'])
#         Utils.assert_not_contains(result.json())
#         logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['consensus_params'])
    def test_consensus_params_jsonrpc(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.consensus_params_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['consensus_state'])
    def test_consensus_state_rest(self, env, data):
        result = self.consensus_state_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['consensus_state'])
    def test_consensus_state_jsonrpc(self, env, data):
        result = self.consensus_state_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

  
    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['dump_consensus_state'])
    # def test_dump_consensus_state_rest(self, env, data):
    #     result = self.dump_consensus_state_rest(env['zeta_rpc'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')
    #
    # 
    # @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['dump_consensus_state'])
    # def test_dump_consensus_state_jsonrpc(self, env, data):
    #     result = self.dump_consensus_state_jsonrpc(env['zeta_rpc'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_id_and_version(result.json())
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['genesis_chunked'])
    def test_genesis_chunked_rest(self, env, data):
        result = self.genesis_chunked_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['genesis_chunked'])
    def test_genesis_chunked_jsonrpc(self, env, data):
        result = self.genesis_chunked_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['health'])
    def test_health_rest(self, env, data):
        result = self.health_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['health'])
    def test_health_jsonrpc(self, env, data):
        result = self.health_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['num_unconfirmed_txs'])
    def test_num_unconfirmed_txs_rest(self, env, data):
        result = self.num_unconfirmed_txs_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['num_unconfirmed_txs'])
    def test_num_unconfirmed_txs_jsonrpc(self, env, data):
        result = self.num_unconfirmed_txs_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['status'])
    def test_status_rest(self, env, data):
        result = self.status_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['status'])
    def test_status_jsonrpc(self, env, data):
        result = self.status_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['tx'])
    # def test_tx_rest(self, env, data):
    #     result = self.tx_rest(env['zeta_rpc'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['net_info'])
    def test_net_info_rest(self, env, data):
        result = self.net_info_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


    @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['net_info'])
    def test_net_info_jsonrpc(self, env, data):
        result = self.net_info_jsonrpc(env['zeta_rpc'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    
    @pytest.mark.parametrize('data', datas['zeta']['rest_api']['validators'])
    def test_validators_rest(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blocknumber': get_latest_block})
        result = self.validators_rest(env['zeta_rpc'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')


    # @pytest.mark.parametrize('data', datas['zeta']['rest_api']['check_tx'])
    # def test_check_tx_rest(self, env, data):
    #     result = self.check_tx_rest(env['zeta_rpc'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    # @pytest.mark.parametrize('data', datas['zeta']['json_rpc']['check_tx'])
    # def test_check_tx_jsonrpc(self, env, data):
    #     result = self.check_tx_jsonrpc(env['zeta_rpc'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_id_and_version(result.json())
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')