import pytest
from loguru import logger
from common.utils import Utils
from apis.aptos_api import AptosApi


@pytest.mark.movement
class TestMovement(AptosApi):

    datas = AptosApi.data

    @pytest.fixture(scope="class")
    def get_latest_block(self, env):
        latest_block = self.get_ledger_info(env['movement'])
        return latest_block.json()['block_height']

    @pytest.fixture(scope="class")
    def get_last_version(self, env):
        latest_block = self.get_ledger_info(env['movement'])
        return latest_block.json()['ledger_version']

    @pytest.fixture(scope="class")
    def get_last_transaction(self, env):
        latest_block = self.get_transactions(env['movement'], '/transactions?limit=1')
        return latest_block.json()[0]['hash']

    @pytest.mark.parametrize('data', datas['movement']['get_account'])
    def test_get_account(self, env, data):
        result = self.get_account(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['get_account_resources'])
    def test_get_account_resources(self, env, data):
        result = self.get_account_resources(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['get_account_modules'])
    def test_get_account_modules(self, env, data):
        result = self.get_account_modules(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        # Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result)
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['get_account_resource'])
    def test_get_account_resource(self, env, data):
        result = self.get_account_resource(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    # @pytest.mark.parametrize('data', datas['movement']['get_account_module'])
    # def test_get_account_module(self, env, data):
    #     result = self.get_account_module(env['movement'], data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_contains(result.json())
    #     Utils.assert_not_contains(result.json())
    #     logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['get_blocks_by_height'])
    def test_get_blocks_by_height(self, get_latest_block, env, data):
        data = Utils.handle_template(data, {'blockheight': get_latest_block})
        result = self.get_blocks_by_height(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['get_events_by_creation_number'])
    def test_get_events_by_creation_number(self, env, data):
        result = self.get_events_by_creation_number(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['get_blocks_by_version'])
    def test_get_blocks_by_version(self, get_last_version, env, data):
        data = Utils.handle_template(data, {'lastversion': get_last_version})
        result = self.get_blocks_by_version(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['get_events_by_event_handel'])
    def test_get_events_by_event_handel(self, env, data):
        result = self.get_events_by_event_handel(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['show_openapi_explorer'])
    def test_show_openapi_explorer(self, env, data):
        result = self.show_openapi_explorer(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['check_basic_node_health'])
    def test_check_basic_node_health(self, env, data):
        result = self.check_basic_node_health(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['get_ledger_info'])
    def test_get_ledger_info(self, env, data):
        result = self.get_ledger_info(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['get_transactions'])
    def test_get_transactions(self, env, data):
        result = self.get_transactions(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['get_transaction_by_hash'])
    def test_get_transaction_by_hash(self, get_last_transaction, env, data):
        data = Utils.handle_template(data, {'lasttransaction': get_last_transaction})
        result = self.get_transaction_by_hash(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['get_account_transactions'])
    def test_get_account_transactions(self, env, data):
        result = self.get_account_transactions(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['movement']['estimate_gasprice'])
    def test_estimate_gasprice(self, env, data):
        result = self.estimate_gasprice(env['movement'], data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')