import pytest
from loguru import logger
from common.utils import Utils
from apis.solana_api import SolanaApi


@pytest.mark.solana
class TestSolana(SolanaApi):

    datas = SolanaApi.data

    @pytest.fixture(scope="class")
    def get_latest_block_slot(self, env):
        """获取最新的 slot"""
        data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getSlot"
        }
        result = self.getSlot(env['solana'], **data)
        slot = result.json().get('result')
        if slot is None:
            pytest.fail("Failed to get latest slot")
        # 确保返回的是整数类型，而不是字符串
        return int(slot) if isinstance(slot, str) else slot

    @pytest.mark.parametrize('data', datas['solana']['getAccountInfo'])
    def test_getAccountInfo(self, env, data):
        result = self.getAccountInfo(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getBalance'])
    def test_getBalance(self, env, data):
        result = self.getBalance(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getBlock'])
    def test_getBlock(self, env, data, get_latest_block_slot):
        # 手动替换 blockslot 参数，保持数据类型
        test_data = data.copy()
        if 'payload' in test_data and 'params' in test_data['payload']:
            params = test_data['payload']['params'].copy()
            # 替换第一个参数（blockslot）为实际的 slot 数字
            if len(params) > 0 and params[0] == '$blockslot':
                params[0] = get_latest_block_slot
            test_data['payload']['params'] = params

        result = self.getBlock(env['solana'], **test_data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), test_data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getBlockHeight'])
    def test_getBlockHeight(self, env, data):
        result = self.getBlockHeight(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getBlockCommitment'])
    def test_getBlockCommitment(self, env, data, get_latest_block_slot):
        # 手动替换 solana_blockheight 参数，保持数据类型
        test_data = data.copy()
        if 'payload' in test_data and 'params' in test_data['payload']:
            params = test_data['payload']['params'].copy()
            # 替换第一个参数（solana_blockheight）为实际的 slot 数字
            if len(params) > 0 and params[0] == '$solana_blockheight':
                params[0] = get_latest_block_slot
            test_data['payload']['params'] = params

        result = self.getBlockCommitment(env['solana'], **test_data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), test_data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getBlockProduction'])
    def test_getBlockProduction(self, env, data):
        result = self.getBlockProduction(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getBlockTime'])
    def test_getBlockTime(self, env, data, get_latest_block_slot):
        # 手动替换 blockslot 参数，保持数据类型
        test_data = data.copy()
        if 'payload' in test_data and 'params' in test_data['payload']:
            params = test_data['payload']['params'].copy()
            # 替换第一个参数（blockslot）为实际的 slot 数字
            if len(params) > 0 and params[0] == '$blockslot':
                params[0] = get_latest_block_slot
            test_data['payload']['params'] = params

        result = self.getBlockTime(env['solana'], **test_data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), test_data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getEpochInfo'])
    def test_getEpochInfo(self, env, data):
        result = self.getEpochInfo(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getEpochSchedule'])
    def test_getEpochSchedule(self, env, data):
        result = self.getEpochSchedule(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getGenesisHash'])
    def test_getGenesisHash(self, env, data):
        result = self.getGenesisHash(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getIdentity'])
    def test_getIdentity(self, env, data):
        result = self.getIdentity(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getInflationGovernor'])
    def test_getInflationGovernor(self, env, data):
        result = self.getInflationGovernor(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getInflationRate'])
    def test_getInflationRate(self, env, data):
        result = self.getInflationRate(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getLatestBlockhash'])
    def test_getLatestBlockhash(self, env, data):
        result = self.getLatestBlockhash(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getLeaderSchedule'])
    def test_getLeaderSchedule(self, env, data):
        result = self.getLeaderSchedule(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getMaxRetransmitSlot'])
    def test_getMaxRetransmitSlot(self, env, data):
        result = self.getMaxRetransmitSlot(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getMaxShredInsertSlot'])
    def test_getMaxShredInsertSlot(self, env, data):
        result = self.getMaxShredInsertSlot(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getMinimumBalanceForRentExemption'])
    def test_getMinimumBalanceForRentExemption(self, env, data):
        result = self.getMinimumBalanceForRentExemption(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getMultipleAccounts'])
    def test_getMultipleAccounts(self, env, data):
        result = self.getMultipleAccounts(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getRecentPerformanceSamples'])
    def test_getRecentPerformanceSamples(self, env, data):
        result = self.getRecentPerformanceSamples(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getTransaction'])
    def test_getTransaction(self, env, data):
        result = self.getTransaction(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getRecentPrioritizationFees'])
    def test_getRecentPrioritizationFees(self, env, data):
        result = self.getRecentPrioritizationFees(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getSignatureStatuses'])
    def test_getSignatureStatuses(self, env, data):
        result = self.getSignatureStatuses(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getSignaturesForAddress'])
    def test_getSignaturesForAddress(self, env, data):
        result = self.getSignaturesForAddress(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getSlotLeader'])
    def test_getSlotLeader(self, env, data):
        result = self.getSlotLeader(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    # @pytest.mark.parametrize('data', datas['solana']['getSlotLeaders'])
    # def test_getSlotLeaders(self, env, data):
    #     result = self.getSlotLeaders(env['solana'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_id_and_version(result.json())
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getStakeMinimumDelegation'])
    def test_getStakeMinimumDelegation(self, env, data):
        result = self.getStakeMinimumDelegation(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getTokenAccountBalance'])
    def test_getTokenAccountBalance(self, env, data):
        result = self.getTokenAccountBalance(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    # @pytest.mark.parametrize('data', datas['solana']['getTokenAccountsByDelegate'])
    # def test_getTokenAccountsByDelegate(self, env, data):
    #     result = self.getTokenAccountsByDelegate(env['solana'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_id_and_version(result.json())
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getTokenAccountsByOwner'])
    def test_getTokenAccountsByOwner(self, env, data):
        result = self.getTokenAccountsByOwner(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getTokenLargestAccounts'])
    def test_getTokenLargestAccounts(self, env, data):
        result = self.getTokenLargestAccounts(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getTokenSupply'])
    def test_getTokenSupply(self, env, data):
        result = self.getTokenSupply(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getTransactionCount'])
    def test_getTransactionCount(self, env, data):
        result = self.getTransactionCount(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getVersion'])
    def test_getVersion(self, env, data):
        result = self.getVersion(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['getVoteAccounts'])
    def test_getVoteAccounts(self, env, data):
        result = self.getVoteAccounts(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['isBlockhashValid'])
    def test_isBlockhashValid(self, env, data):
        result = self.isBlockhashValid(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['solana']['minimumLedgerSlot'])
    def test_minimumLedgerSlot(self, env, data):
        result = self.minimumLedgerSlot(env['solana'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')
