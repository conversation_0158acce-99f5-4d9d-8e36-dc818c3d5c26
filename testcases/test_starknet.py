import pytest
from loguru import logger
from apis.starknet_api import StarknetApi
from common.utils import Utils


@pytest.mark.starknet
class TestStarknet(StarknetApi):

    datas = StarknetApi.data

    @pytest.mark.parametrize('data', datas['starknet']['starknet_specVersion'])
    def test_starknet_specVersion(self, env, data):
        result = self.starknet_specVersion(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getBlockWithTxHashes'])
    def test_starknet_getBlockWithTxHashes(self, env, data):
        result = self.starknet_getBlockWithTxHashes(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getBlockWithTxs'])
    def test_starknet_getBlockWithTxs(self, env, data):
        result = self.starknet_getBlockWithTxs(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getStateUpdate'])
    def test_starknet_getStateUpdate(self, env, data):
        result = self.starknet_getStateUpdate(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getStorageAt'])
    def test_starknet_getStorageAt(self, env, data):
        result = self.starknet_getStorageAt(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getTransactionStatus'])
    def test_starknet_getTransactionStatus(self, env, data):
        result = self.starknet_getTransactionStatus(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getTransactionByHash'])
    def test_starknet_getTransactionByHash(self, env, data):
        result = self.starknet_getTransactionByHash(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getTransactionByBlockIdAndIndex'])
    def test_starknet_getTransactionByBlockIdAndIndex(self, env, data):
        result = self.starknet_getTransactionByBlockIdAndIndex(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getTransactionReceipt'])
    def test_starknet_getTransactionReceipt(self, env, data):
        result = self.starknet_getTransactionReceipt(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getClass'])
    def test_starknet_getClass(self, env, data):
        result = self.starknet_getClass(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getClassHashAt'])
    def test_starknet_getClassHashAt(self, env, data):
        result = self.starknet_getClassHashAt(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getClassAt'])
    def test_starknet_getClassAt(self, env, data):
        result = self.starknet_getClassAt(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getBlockTransactionCount'])
    def test_starknet_getBlockTransactionCount(self, env, data):
        result = self.starknet_getBlockTransactionCount(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_call'])
    def test_starknet_call(self, env, data):
        result = self.starknet_call(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_blockNumber'])
    def test_starknet_blockNumber(self, env, data):
        result = self.starknet_blockNumber(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_blockHashAndNumber'])
    def test_starknet_blockHashAndNumber(self, env, data):
        result = self.starknet_blockHashAndNumber(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_chainId'])
    def test_starknet_chainId(self, env, data):
        result = self.starknet_chainId(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_syncing'])
    def test_starknet_syncing(self, env, data):
        result = self.starknet_syncing(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getEvents'])
    def test_starknet_getEvents(self, env, data):
        result = self.starknet_getEvents(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['starknet']['starknet_getNonce'])
    def test_starknet_getNonce(self, env, data):
        result = self.starknet_getNonce(env['starknet'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')


