import pytest
from loguru import logger
from common.utils import Utils
from apis.solana_api import SolanaApi


@pytest.mark.eclipse
class TestEclipse(SolanaApi):

    datas = SolanaApi.data

    @pytest.fixture(scope="class")
    def get_latest_block_slot(self, env):
        """获取最新的 slot"""
        data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getSlot"
        }
        result = self.getSlot(env['eclipse'], **data)
        slot = result.json().get('result')
        if slot is None:
            pytest.fail("Failed to get latest slot")
        # 确保返回的是整数类型，而不是字符串
        return int(slot) if isinstance(slot, str) else slot

    @pytest.mark.parametrize('data', datas['eclipse']['getBalance'])
    def test_getBalance(self, env, data):
        """测试 getBalance 方法"""
        result = self.getBalance(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getBlock'])
    def test_getBlock(self, env, data, get_latest_block_slot):
        """测试 getBlock 方法"""
        # 手动替换 blockslot 参数，保持数据类型
        test_data = data.copy()
        if 'payload' in test_data and 'params' in test_data['payload']:
            params = test_data['payload']['params'].copy()
            # 替换第一个参数（blockslot）为实际的 slot 数字
            if len(params) > 0 and params[0] == '$blockslot':
                params[0] = get_latest_block_slot
            test_data['payload']['params'] = params
        
        result = self.getBlock(env['eclipse'], **test_data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), test_data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getBlockCommitment'])
    def test_getBlockCommitment(self, env, data, get_latest_block_slot):
        """测试 getBlockCommitment 方法"""
        # 手动替换 eclipse_blockheight 参数，保持数据类型
        test_data = data.copy()
        if 'payload' in test_data and 'params' in test_data['payload']:
            params = test_data['payload']['params'].copy()
            # 替换第一个参数（eclipse_blockheight）为实际的 slot 数字
            if len(params) > 0 and params[0] == '$eclipse_blockheight':
                params[0] = get_latest_block_slot
            test_data['payload']['params'] = params
        
        result = self.getBlockCommitment(env['eclipse'], **test_data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), test_data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getBlockProduction'])
    def test_getBlockProduction(self, env, data):
        """测试 getBlockProduction 方法"""
        result = self.getBlockProduction(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getBlockHeight'])
    def test_getBlockHeight(self, env, data):
        """测试 getBlockHeight 方法"""
        result = self.getBlockHeight(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getAccountInfo'])
    def test_getAccountInfo(self, env, data):
        """测试 getAccountInfo 方法"""
        result = self.getAccountInfo(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getLatestBlockhash'])
    def test_getLatestBlockhash(self, env, data):
        """测试 getLatestBlockhash 方法"""
        result = self.getLatestBlockhash(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getEpochInfo'])
    def test_getEpochInfo(self, env, data):
        """测试 getEpochInfo 方法"""
        result = self.getEpochInfo(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getVersion'])
    def test_getVersion(self, env, data):
        """测试 getVersion 方法"""
        result = self.getVersion(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getSlot'])
    def test_getSlot(self, env, data):
        """测试 getSlot 方法"""
        result = self.getSlot(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getEpochSchedule'])
    def test_getEpochSchedule(self, env, data):
        """测试 getEpochSchedule 方法"""
        result = self.getEpochSchedule(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getGenesisHash'])
    def test_getGenesisHash(self, env, data):
        """测试 getGenesisHash 方法"""
        result = self.getGenesisHash(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getIdentity'])
    def test_getIdentity(self, env, data):
        """测试 getIdentity 方法"""
        result = self.getIdentity(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getInflationGovernor'])
    def test_getInflationGovernor(self, env, data):
        """测试 getInflationGovernor 方法"""
        result = self.getInflationGovernor(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getInflationRate'])
    def test_getInflationRate(self, env, data):
        """测试 getInflationRate 方法"""
        result = self.getInflationRate(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getLeaderSchedule'])
    def test_getLeaderSchedule(self, env, data):
        """测试 getLeaderSchedule 方法"""
        result = self.getLeaderSchedule(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getMaxRetransmitSlot'])
    def test_getMaxRetransmitSlot(self, env, data):
        """测试 getMaxRetransmitSlot 方法"""
        result = self.getMaxRetransmitSlot(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getMaxShredInsertSlot'])
    def test_getMaxShredInsertSlot(self, env, data):
        """测试 getMaxShredInsertSlot 方法"""
        result = self.getMaxShredInsertSlot(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getMinimumBalanceForRentExemption'])
    def test_getMinimumBalanceForRentExemption(self, env, data):
        """测试 getMinimumBalanceForRentExemption 方法"""
        result = self.getMinimumBalanceForRentExemption(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getMultipleAccounts'])
    def test_getMultipleAccounts(self, env, data):
        """测试 getMultipleAccounts 方法"""
        result = self.getMultipleAccounts(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getRecentPerformanceSamples'])
    def test_getRecentPerformanceSamples(self, env, data):
        """测试 getRecentPerformanceSamples 方法"""
        result = self.getRecentPerformanceSamples(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getTransaction'])
    def test_getTransaction(self, env, data):
        """测试 getTransaction 方法"""
        result = self.getTransaction(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getRecentPrioritizationFees'])
    def test_getRecentPrioritizationFees(self, env, data):
        """测试 getRecentPrioritizationFees 方法"""
        result = self.getRecentPrioritizationFees(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getSignatureStatuses'])
    def test_getSignatureStatuses(self, env, data):
        """测试 getSignatureStatuses 方法"""
        result = self.getSignatureStatuses(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getSignaturesForAddress'])
    def test_getSignaturesForAddress(self, env, data):
        """测试 getSignaturesForAddress 方法"""
        result = self.getSignaturesForAddress(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getSlotLeader'])
    def test_getSlotLeader(self, env, data):
        """测试 getSlotLeader 方法"""
        result = self.getSlotLeader(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    # @pytest.mark.parametrize('data', datas['eclipse']['getSlotLeaders'])
    # def test_getSlotLeaders(self, env, data):
    #     """测试 getSlotLeaders 方法"""
    #     result = self.getSlotLeaders(env['eclipse'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_id_and_version(result.json())
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getStakeMinimumDelegation'])
    def test_getStakeMinimumDelegation(self, env, data):
        """测试 getStakeMinimumDelegation 方法"""
        result = self.getStakeMinimumDelegation(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getTokenAccountBalance'])
    def test_getTokenAccountBalance(self, env, data):
        """测试 getTokenAccountBalance 方法"""
        result = self.getTokenAccountBalance(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    # @pytest.mark.parametrize('data', datas['eclipse']['getTokenAccountsByDelegate'])
    # def test_getTokenAccountsByDelegate(self, env, data):
    #     """测试 getTokenAccountsByDelegate 方法"""
    #     result = self.getTokenAccountsByDelegate(env['eclipse'], **data['payload'])
    #     Utils.assert_status_and_nodeid(result)
    #     Utils.assert_id_and_version(result.json())
    #     Utils.assert_contains(result.json(), data['expected'])
    #     logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getTokenAccountsByOwner'])
    def test_getTokenAccountsByOwner(self, env, data):
        """测试 getTokenAccountsByOwner 方法"""
        result = self.getTokenAccountsByOwner(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getTokenLargestAccounts'])
    def test_getTokenLargestAccounts(self, env, data):
        """测试 getTokenLargestAccounts 方法"""
        result = self.getTokenLargestAccounts(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getTokenSupply'])
    def test_getTokenSupply(self, env, data):
        """测试 getTokenSupply 方法"""
        result = self.getTokenSupply(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getTransactionCount'])
    def test_getTransactionCount(self, env, data):
        """测试 getTransactionCount 方法"""
        result = self.getTransactionCount(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['getVoteAccounts'])
    def test_getVoteAccounts(self, env, data):
        """测试 getVoteAccounts 方法"""
        result = self.getVoteAccounts(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['isBlockhashValid'])
    def test_isBlockhashValid(self, env, data):
        """测试 isBlockhashValid 方法"""
        result = self.isBlockhashValid(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['eclipse']['minimumLedgerSlot'])
    def test_minimumLedgerSlot(self, env, data):
        """测试 minimumLedgerSlot 方法"""
        result = self.minimumLedgerSlot(env['eclipse'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')
