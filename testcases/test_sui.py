import pytest
from loguru import logger
from apis.sui_api import SuiApi
from common.utils import Utils


@pytest.mark.sui
class TestSui(SuiApi):

    datas = SuiApi.data

    @pytest.mark.parametrize('data', datas['sui']['suix_get_all_balances'])
    def test_suix_get_all_balances(self, env, data):
        result = self.suix_get_all_balances(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['suix_get_all_coins'])
    def test_suix_get_all_coins(self, env, data):
        result = self.suix_get_all_coins(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['suix_get_balance'])
    def test_suix_get_balance(self, env, data):
        result = self.suix_get_balance(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['suix_get_coin_metadata'])
    def test_suix_get_coin_metadata(self, env, data):
        result = self.suix_get_coin_metadata(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['suix_get_coins'])
    def test_suix_get_coins(self, env, data):
        result = self.suix_get_coins(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['suix_get_latest_sui_system_state'])
    def test_suix_get_latest_sui_system_state(self, env, data):
        result = self.suix_get_latest_sui_system_state(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['suix_get_reference_gas_price'])
    def test_suix_get_reference_gas_price(self, env, data):
        result = self.suix_get_reference_gas_price(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['suix_get_validators_apy'])
    def test_suix_get_validators_apy(self, env, data):
        result = self.suix_get_validators_apy(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_move_function_arg_types'])
    def test_sui_get_move_function_arg_types(self, env, data):
        result = self.sui_get_move_function_arg_types(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_normalized_move_function'])
    def test_sui_get_normalized_move_function(self, env, data):
        result = self.sui_get_normalized_move_function(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_normalized_move_module'])
    def test_sui_get_normalized_move_module(self, env, data):
        result = self.sui_get_normalized_move_module(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_normalized_move_modules_by_package'])
    def test_sui_get_normalized_move_modules_by_package(self, env, data):
        result = self.sui_get_normalized_move_modules_by_package(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_normalized_move_struct'])
    def test_sui_get_normalized_move_struct(self, env, data):
        result = self.sui_get_normalized_move_struct(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_chain_identifier'])
    def test_sui_get_chain_identifier(self, env, data):
        result = self.sui_get_chain_identifier(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_checkpoint'])
    def test_sui_get_checkpoint(self, env, data):
        result = self.sui_get_checkpoint(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_checkpoints'])
    def test_sui_get_checkpoints(self, env, data):
        result = self.sui_get_checkpoints(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_events'])
    def test_sui_get_events(self, env, data):
        result = self.sui_get_events(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_latest_checkpoint_sequence_number'])
    def test_sui_get_latest_checkpoint_sequence_number(self, env, data):
        result = self.sui_get_latest_checkpoint_sequence_number(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_protocol_config'])
    def test_sui_get_protocol_config(self, env, data):
        result = self.sui_get_protocol_config(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_total_transaction_blocks'])
    def test_sui_get_total_transaction_blocks(self, env, data):
        result = self.sui_get_total_transaction_blocks(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_get_transaction_block'])
    def test_sui_get_transaction_block(self, env, data):
        result = self.sui_get_transaction_block(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_multi_get_objects'])
    def test_sui_multi_get_objects(self, env, data):
        result = self.sui_multi_get_objects(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_try_multi_get_past_objects'])
    def test_sui_try_multi_get_past_objects(self, env, data):
        result = self.sui_try_multi_get_past_objects(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')

    @pytest.mark.parametrize('data', datas['sui']['sui_multi_get_transaction_blocks'])
    def test_sui_multi_get_transaction_blocks(self, env, data):
        result = self.sui_multi_get_transaction_blocks(env['sui'], **data['payload'])
        Utils.assert_status_and_nodeid(result)
        Utils.assert_id_and_version(result.json())
        Utils.assert_contains(result.json(), data['expected'])
        Utils.assert_not_contains(result.json())
        logger.info('用例通过！')