name: Deploy to Test Servers (Matrix)  # 使用 matrix 策略并行部署

on:
  push:
    branches: [ master ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    strategy:
      fail-fast: false # 允许一个Job失败，其他Job继续运行
      matrix:
        # 定义要部署的服务器列表
        # host_secret_name 对应 GitHub Secrets 中的主机名 Secret
        # server_name 仅用于显示在 Job 名称中，方便识别
        server:
          - { host_secret_name: DEPLOY_HOST_SERVER_A, server_name: "snap1" }
          - { host_secret_name: DEPLOY_HOST_SERVER_B, server_name: "node-usw-ut-web-3" }
          - { host_secret_name: DEPLOY_HOST_SERVER_C, server_name: "VM-0-14-ubuntu" } # 添加更多服务器

    name: Deploy to ${{ matrix.server.server_name }} # Job 名称会显示当前部署的服务器

    steps:
    - uses: actions/checkout@v4
      
    - name: Deploy and Run Tests
      uses: appleboy/ssh-action@v0.1.10
      with:
        host: ${{ secrets[matrix.server.host_secret_name] }} # 动态获取主机名 Secret
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}  # 使用同一份私钥
        port: 22
        timeout: 60s
        debug: true  # 启用调试模式
        script: |
          set -e # 任何命令失败立即退出
          echo "Deploying to ${{ matrix.server.server_name }}..."
          whoami
          pwd
          # 确保系统包是最新的
          sudo apt-get update
          
          # 更新代码
          cd /home/<USER>/test_jsonrpc
          git reset --hard
          git pull origin master
          echo "Deployment to ${{ matrix.server.server_name }} complete."

          # # 以下是测试相关的命令，暂时注释掉
          # python3 -m pip install --upgrade pip
          # python3 -m pip install -r requirements.txt
          # 
          # # 直接运行测试
          # echo "Running tests..."
          # python3 -m pytest -v -m "bsc" --env alphanet
          # 
          # # 检查测试结果
          # if [ $? -ne 0 ]; then
          #   echo "Tests failed"
          #   exit 1
          # fi
          # 
          # echo "All tests passed successfully"
        