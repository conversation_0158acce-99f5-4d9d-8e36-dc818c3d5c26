"""
基础API类模块

提供通用的配置管理功能，所有API类都应该继承此类以获得配置热更新能力。
"""

from typing import Dict, Any, Optional
from common.config_manager import get_config_manager
from common.utils import Utils
from common.types import HttpResponse, HttpPayload, ConfigDict, TestCaseData
from loguru import logger


class BaseApi:
    """
    基础API类

    提供配置管理、请求头获取等通用功能。所有API类都应该继承此类
    以获得统一的配置管理和HTTP请求能力。

    Features:
        - 配置热更新：自动检测配置文件变更并重新加载
        - 统一请求头管理：动态获取最新的请求头配置
        - 测试数据管理：提供测试用例数据的统一访问接口
        - HTTP请求封装：提供通用的HTTP请求方法

    Attributes:
        _config_manager: 配置管理器实例，提供配置缓存和热更新功能
    """

    # 类级别的配置管理器，避免__init__方法影响测试类继承
    _config_manager = get_config_manager()
    
    @property
    def headers(self) -> Dict[str, str]:
        """
        动态获取请求头，支持配置热更新

        Returns:
            Dict[str, str]: 请求头字典，包含Content-Type、User-Agent等标准HTTP头

        Example:
            >>> api = BaseApi()
            >>> headers = api.headers
            >>> print(headers['Content-Type'])
            application/json
        """
        return self._config_manager.get_headers()
    
    @property
    def data(self) -> TestCaseData:
        """
        动态获取测试用例数据，支持配置热更新

        Returns:
            TestCaseData: 测试用例数据字典，按链名和方法名组织

        Example:
            >>> api = BaseApi()
            >>> test_data = api.data
            >>> eth_chain_id_tests = test_data['ethereum']['eth_chainId']
            >>> print(len(eth_chain_id_tests))
            3
        """
        return self._config_manager.get_case_data()
    
    def get_url_config(self, env_name: str = 'alphanet'):
        """
        获取指定环境的URL配置
        
        :param env_name: 环境名称
        :return: URL配置字典
        """
        return self._config_manager.get_url_config(env_name)
    
    def reload_config(self):
        """
        重新加载配置文件
        """
        self._config_manager.reload_config()
        logger.info(f"{self.__class__.__name__} 配置已重新加载")
    
    def _make_http_request(self, url, method='post', headers=None, **kwargs):
        """
        通用的HTTP请求方法
        
        :param url: 请求URL
        :param method: 请求方法
        :param headers: 请求头（可选，默认使用配置的headers）
        :param kwargs: 其他请求参数
        :return: HTTP响应对象
        """
        if headers is None:
            headers = self.headers
        
        payload = {
            'url': url,
            'method': method,
            'headers': headers,
            **kwargs
        }
        
        return Utils.send_http(payload)
