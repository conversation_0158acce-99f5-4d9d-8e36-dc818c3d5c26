from common.handle_path import CASE_DIR, CONFIG_DIR
from common.utils import Utils
from common.wrapper import api_call
from common.config_manager import get_config_manager


class NearApi:

    # 为了向后兼容，保留类级别的data属性
    _config_manager = get_config_manager()
    data = _config_manager.get_case_data()
    headers = _config_manager.get_headers()

    # 预先获取最新区块
    def get_block_height(self, url):
        data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "block",
            "params": {
                "finality": "final"
            }
        }
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def block(self, url, **data):
        """区块详情"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def view_access_key(self, url, **data):
        """查看访问密钥"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def view_access_key_list(self, url, **data):
        """查看访问密钥列表"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def single_access_key_changes(self, url, **data):
        """查看访问密钥更改（单个）"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def all_access_key_changes(self, url, **data):
        """查看访问密钥更改（全部）"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def view_account(self, url, **data):
        """返回基本账户信息"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def account_changes(self, url, **data):
        """返回给定账户交易中的账户变动"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def view_code(self, url, **data):
        """返回部署到账户的合同代码"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def view_state(self, url, **data):
        """查看合约状态"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def data_changes(self, url, **data):
        """查看合约状态变化"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def data_changes(self, url, **data):
        """查看合约状态变化"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def contract_code_changes(self, url, **data):
        """查看合约代码更改"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def call_function(self, url, **data):
        """调用合同函数"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def changes_in_block(self, url, **data):
        """区块变更"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def chunk(self, url, **data):
        """块详情"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def gas_price(self, url, **data):
        """返回特定的 block_height 或 block_hash 的 Gas 价格"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def genesis_config(self, url, **data):
        """返回最近的协议配置或特定查询的区块"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def protocol_config(self, url, **data):
        """获取当前的创世区块和协议配置"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def status(self, url, **data):
        """给定节点的一般状态（同步状态、近核节点版本、协议版本等），以及当前验证者集"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def network_info(self, url, **data):
        """返回节点网络连接的当前状态（活动的对等、传输的数据等）"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validators(self, url, **data):
        """查询网络上活跃的验证器，返回详细信息和区块链上的验证状态。"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def tx(self, url, **data):
        """通过哈希值查询事务状态，并返回最终事务结果。"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def tx_status(self, url, **data):
        """通过哈希值查询交易状态，返回最终交易结果和所有收据的详细信息。"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def receipt(self, url, **data):
        """根据 ID 抓取收据"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def maintenance_windows(self, url, **data):
        """查询当前纪元特定验证器的未来维护窗口"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response