from common.handle_path import CASE_DIR
from common.utils import Utils
from common.wrapper import api_call


class TonApi:

    data = Utils.handle_yaml(CASE_DIR)

    @api_call
    def getAddressInformation(self, url, path):
        """获取地址信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def getExtendedAddressInformation(self, url, path):
        """获取扩展地址信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def getWalletInformation(self, url, path):
        """获取钱包信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def getTransactions(self, url, path):
        """获取给定地址的交易历史"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def getAddressBalance(self, url, path):
        """获取地址余额"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def getAddressState(self, url, path):
        """获取给定地址的状态"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def packAddress(self, url, path):
        """将地址从原始格式转换为人类可读格式"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def unpackAddress(self, url, path):
        """将地址从可读格式转换为原始格式"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def getTokenData(self, url, path):
        """获取 NFT 或 Jetton 信息"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def detectAddress(self, url, path):
        """获取所有可能的地址形式"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def getMasterchainInfo(self, url, path):
        """获取最新的主链状态"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def getConsensusBlock(self, url, path):
        """获取共识块及其更新时间戳"""
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response