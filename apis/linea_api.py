from common.handle_path import CASE_DIR, CONFIG_DIR
from common.utils import Utils
from common.wrapper import api_call


class LineaApi:

    # 读取测试用例文件数据
    data = Utils.handle_yaml(CASE_DIR)
    header = Utils.handle_yaml(CONFIG_DIR)
    headers = header['request_headers']['headers']

    @api_call
    def linea_estimateGas(self, url, **data):
        """预估燃气费"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def linea_getTransactionExclusionStatusV1(self, url, **data):
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def linea_getProof(self, url, **data):
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response