"""
JSON-RPC API 模块

完成测试数据的组装，通过调用不同的接口来实现具体业务逻辑。
提供以太坊兼容的JSON-RPC方法调用功能。
"""

from typing import Dict, Any, Union, Optional, List
from common.handle_path import CONFIG_DIR, CASE_DIR
from common.utils import Utils
from common.wrapper import api_call
from common.types import HttpResponse, JsonDict, RpcRequest, TestCaseData
from types import SimpleNamespace

class JsonrpcApi:
    """
    JSON-RPC API类

    提供以太坊兼容的JSON-RPC方法调用功能，包括：
    - 基础链信息查询 (eth_chainId, eth_blockNumber等)
    - 区块和交易查询 (eth_getBlockByNumber, eth_getTransactionByHash等)
    - 账户相关查询 (eth_getBalance, eth_getTransactionCount等)
    - 智能合约调用 (eth_call, eth_estimateGas等)
    - 过滤器和日志 (eth_getLogs, eth_newFilter等)

    Attributes:
        header: 配置文件头部信息
        headers: HTTP请求头
        data: 测试用例数据
    """

    # 为了向后兼容，保留类级别的data属性
    # 测试用例需要在类级别访问测试数据
    header: JsonDict = Utils.handle_yaml(CONFIG_DIR)
    headers: Dict[str, str] = header['request_headers']['headers']
    data: TestCaseData = Utils.handle_yaml(CASE_DIR)

    def _make_rpc_call(self, url: str, **data: Any) -> HttpResponse:
        """
        通用的RPC调用方法，用于消除重复代码

        Args:
            url: RPC端点URL
            **data: JSON-RPC请求数据，包含method、params、id等字段

        Returns:
            HttpResponse: HTTP响应对象

        Example:
            >>> api = JsonrpcApi()
            >>> response = api._make_rpc_call(
            ...     'https://api.example.com/rpc',
            ...     jsonrpc='2.0',
            ...     method='eth_chainId',
            ...     id=1
            ... )
            >>> print(response.status_code)
            200
        """
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response



    # 预先获取区块数据
    def get_block(self, url, block_identifier, full_transactions=True):
        """
        使用 eth_getBlockByNumber 或 eth_getBlockByHash RPC 方法获取区块信息。
        """
        method = "eth_getBlockByNumber"
        # 检查 block_identifier 是否可能是区块哈希 (0x 开头，长度 66)
        if isinstance(block_identifier, str) and block_identifier.startswith("0x") and len(block_identifier) == 66:
             method = "eth_getBlockByHash"
             params = [block_identifier, full_transactions]
        # 处理区块号整数
        elif isinstance(block_identifier, int):
            params = [hex(block_identifier), full_transactions]
        # 处理 'latest', 'earliest', 'pending' 等字符串标识符
        elif isinstance(block_identifier, str):
             params = [block_identifier, full_transactions]
        else:
            raise ValueError(f"Invalid block identifier: {block_identifier}")

        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': {
                "jsonrpc": "2.0",
                "method": method,
                "params": params,
                "id": 1
            }
        }
        response = Utils.send_http(payload)
        response_json = response.json()

        if "error" in response_json:
            # Consider logging the error or raising a more specific exception
            print(f"Error fetching block: {response_json['error']}")
            return None
        elif "result" not in response_json:
             print(f"Unexpected response format (missing 'result'): {response_json}")
             return None

        # web3.py's get_block performs some type conversions (e.g., hex str to int for number).
        # We return the raw dict for now. Adjust fixtures if needed.
        return response_json.get("result")

    def get_new_block_filter(self, url):
        """
        使用 eth_newBlockFilter RPC 方法创建新的区块过滤器。
        """
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': {
                "jsonrpc": "2.0",
                "method": "eth_newBlockFilter",
                "params": [],
                "id": 1
            }
        }
        response = Utils.send_http(payload)
        response_json = response.json()

        if "error" in response_json:
            print(f"Error creating block filter: {response_json['error']}")
            return None
        elif "result" not in response_json:
             print(f"Unexpected response format (missing 'result'): {response_json}")
             return None

        filter_id = response_json.get("result")
        # 返回一个带有 filter_id 属性的对象，以保持兼容性
        return SimpleNamespace(filter_id=filter_id)

    @api_call
    def eth_chainId(self, url, **data):    # 关键字参数
        """返回当前配置的链 ID"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_syncing(self, url, **data):
        """返回一个对象，其中包含有关同步状态的数据或 false"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getBlockByNumber(self, url, **data):
        """根据区块编号返回关于区块的信息"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getBlockByHash(self, url, **data):
        """根据哈希返回区块信息"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_blockNumber(self, url, **data):
        """返回最新区块的编号"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_gasPrice(self, url, **data):
        """返回以 wei 为单位的当前 gas 价格"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getBalance(self, url, **data):
        """返回给定地址的账户余额"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getTransactionByHash(self, url, **data):
        """返回关于按交易哈希请求的交易的信息"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getTransactionByBlockHashAndIndex(self, url, **data):
        """根据区块哈希和交易索引位置返回关于交易的信息"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getTransactionByBlockNumberAndIndex(self, url, **data):
        """根据区块编号和交易索引位置返回关于交易的信息"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getTransactionReceipt(self, url, **data):
        """根据交易哈希返回交易的收据"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getTransactionCount(self, url, **data):
        """返回从一个地址发送的交易数量"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getBlockTransactionCountByHash(self, url, **data):
        """返回匹配给定区块哈希的区块中的交易数量"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getBlockTransactionCountByNumber(self, url, **data):
        """返回匹配给定区块编号的区块中的交易数量"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getLogs(self, url, **data):
        """返回与给定过滤器对象匹配的所有日志的数组"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getCode(self, url, **data):
        """返回位于给定地址的代码"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_call(self, url, **data):
        """立即执行新的消息调用，而不在区块链上创建交易"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getStorageAt(self, url, **data):
        """从给定地址的存储位置返回值"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_estimateGas(self, url, **data):
        """生成并返回允许交易完成所需燃料数量的估算值"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_newFilter(self, url, **data):
        """基于过滤器选项创建一个过滤器对象，以在状态更改（日志）时发出通知"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_newBlockFilter(self, url, **data):
        """在节点中创建一个过滤器，以在新区块到达时发出通知"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_newPendingTransactionFilter(self, url, **data):
        """在节点中创建一个过滤器，以在新的待处理交易到达时发出通知"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getFilterChanges(self, url, **data):
        """过滤器的轮询方法，会返回自上次轮询以来产生的日志数组"""
        return self._make_rpc_call(url, **data)

    @api_call
    def net_version(self, url, **data):
        """返回当前网络 id"""
        return self._make_rpc_call(url, **data)

    @api_call
    def net_listening(self, url, **data):
        """如果客户端正在主动监听网络连接，则返回 true"""
        return self._make_rpc_call(url, **data)

    @api_call
    def net_peerCount(self, url, **data):
        """返回当前连接到客户端的对等点数"""
        return self._make_rpc_call(url, **data)

    @api_call
    def web3_clientVersion(self, url, **data):
        """返回当前客户端版本"""
        return self._make_rpc_call(url, **data)

    @api_call
    def web3_sha3(self, url, **data):
        """返回给定数据的 Keccak-256"""
        return self._make_rpc_call(url, **data)

    @api_call
    def txpool_status(self, url, **data):
        """返回当前待包含在下一个块中的交易数量，以及计划仅在将来执行的交易数量"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getBlockReceipts(self, url, **data):
        """获取给定区块的所有交易收据"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_estimateFee(self, url, **data):
        """返回交易费用"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_estimateGasL1ToL2(self, url, **data):
        """返回 L1 到 L2 交易所需的 Gas 估计值"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getAllAccountBalances(self, url, **data):
        """返回帐户地址给出的已确认代币的所有余额"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getBlockDetails(self, url, **data):
        """返回有关 L2 块的附加 zkSync 特定信息"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getBridgeContracts(self, url, **data):
        """返回默认网桥的 L1/L2 地址"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getBytecodeByHash(self, url, **data):
        """返回默认网桥的 L1/L2 地址"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getConfirmedTokens(self, url, **data):
        """返回由参数和给定的 id 范围内的所有令牌的 [地址、符号、名称和小数] 信息"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getL1BatchBlockRange(self, url, **data):
        """返回由批次号给定的批次中包含的块的范围"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getL2ToL1LogProof(self, url, **data):
        """返回相应的 L2 到 L1 的日志证明"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getMainContract(self, url, **data):
        """返回 zkSync Era 合约的地址"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getRawBlockTransactions(self, url, **data):
        """返回块中交易的数据"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getTestnetPaymaster(self, url, **data):
        """返回测试网 paymaster的地址"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getTokenPrice(self, url, **data):
        """返回给定代币的美元价格"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_getTransactionDetails(self, url, **data):
        """返回由交易哈希给出的特定交易的数据"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_L1BatchNumber(self, url, **data):
        """返回最新的 L1 批次号"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zks_L1ChainId(self, url, **data):
        """返回底层 L1 的链 id"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getCompilers(self, url, **data):
        """返回客户端中可用编译器的列表"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getUncleCountByBlockHash(self, url, **data):
        """返回与给定块哈希匹配的块的叔叔数"""
        return self._make_rpc_call(url, **data)

    @api_call
    def eth_getUncleCountByBlockNumber(self, url, **data):
        """返回与给定块号匹配的块的叔叔数"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zkevm_batchNumber(self, url, **data):
        """返回最新的批次号"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zkevm_batchNumberByBlockNumber(self, url, **data):
        """返回给定块号所属的批次号"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zkevm_consolidatedBlockNumber(self, url, **data):
        """返回连接到最新块的最新验证批次的块号"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zkevm_getBatchByNumber(self, url, **data):
        """根据批次号返回 ZK-EVM 层中特定批次的信息"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zkevm_isBlockConsolidated(self, url, **data):
        """如果提供的块号已连接到已验证的批次，则返回 true，否则返回 false"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zkevm_isBlockVirtualized(self, url, **data):
        """如果给定的块号连接到已虚拟化的批次，则返回 true，否则返回 false"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zkevm_verifiedBatchNumber(self, url, **data):
        """返回最新验证的批次号"""
        return self._make_rpc_call(url, **data)

    @api_call
    def zkevm_virtualBatchNumber(self, url, **data):
        """返回最新的虚拟批次号"""
        return self._make_rpc_call(url, **data)

    @api_call
    def disabled_method(self, url, **data):
        """被禁用方法"""
        return self._make_rpc_call(url, **data)

    @api_call
    def trace_block(self, url, **data):
        """获取给定块中所有交易的跟踪"""
        return self._make_rpc_call(url, **data)

    @api_call
    def trace_call(self, url, **data):
        """执行给定的调用并返回它的许多可能的跟踪"""
        return self._make_rpc_call(url, **data)

    @api_call
    def trace_get(self, url, **data):
        """返回给定位置的轨迹"""
        return self._make_rpc_call(url, **data)

    @api_call
    def trace_filter(self, url, **data):
        """返回与给定过滤器匹配的跟踪"""
        return self._make_rpc_call(url, **data)

    @api_call
    def trace_transaction(self, url, **data):
        """返回给定交易的所有痕迹"""
        return self._make_rpc_call(url, **data)

    @api_call
    def trace_replayTransaction(self, url, **data):
        """跟踪对 eth_sendRawTransaction 的调用而不进行调用，返回跟踪"""
        return self._make_rpc_call(url, **data)

    @api_call
    def debug_traceTransaction(self, url, **data):
        """尝试以与在网络上执行的完全相同的方式运行事务"""
        return self._make_rpc_call(url, **data)

    @api_call
    def debug_traceBlockByHash(self, url, **data):
        """重播数据库中已存在的块"""
        return self._make_rpc_call(url, **data)

    @api_call
    def debug_traceBlockByNumber(self, url, **data):
        """重播数据库中已存在的块"""
        return self._make_rpc_call(url, **data)

    @api_call
    def batchCall(self, url, data):
        """批量执行消息调用"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response