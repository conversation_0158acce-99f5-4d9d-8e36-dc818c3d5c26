from common.utils import Utils
from common.wrapper import api_call
from common.handle_path import CONFIG_DIR, CASE_DIR


class StarknetApi:

    header = Utils.handle_yaml(CONFIG_DIR)
    headers = header['request_headers']['headers']
    # 读取测试用例文件数据
    data = Utils.handle_yaml(CASE_DIR)

    @api_call
    def starknet_specVersion(self, url, **data):
        """返回正在使用的 Starknet JSON-RPC 规范的版本"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getBlockWithTxHashes(self, url, **data):
        """根据给定的区块 ID 获取交易哈希值的区块信息"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getBlockWithTxs(self, url, **data):
        """获取给定区块 ID 的完整交易的区块信息"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getStateUpdate(self, url, **data):
        """获取有关执行请求块的结果的信息"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getStorageAt(self, url, **data):
        """获取给定地址和键的存储值"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getTransactionStatus(self, url, **data):
        """获取交易状态"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getTransactionByHash(self, url, **data):
        """获取已提交交易的详细信息和状态"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getTransactionByBlockIdAndIndex(self, url, **data):
        """获取由所识别的块和该块中的索引给出的交易的详细信息"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getTransactionReceipt(self, url, **data):
        """通过交易哈希获取交易收据"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getClass(self, url, **data):
        """获取与给定哈希关联的合约类定义"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getClassHashAt(self, url, **data):
        """获取给定地址部署的合约的给定块中的合约类哈希值"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getClassAt(self, url, **data):
        """获取给定地址给定块中的合约类定义"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getBlockTransactionCount(self, url, **data):
        """通过交易哈希返回交易的收据"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_call(self, url, **data):
        """调用 starknet 函数而不创建 StarkNet 交易"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_blockNumber(self, url, **data):
        """获取最近接受的区块号"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_blockHashAndNumber(self, url, **data):
        """获取最新接受的区块哈希值和编号"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_chainId(self, url, **data):
        """返回当前配置的StarkNet链id"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_syncing(self, url, **data):
        """返回有关同步状态的对象"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getEvents(self, url, **data):
        """返回与给定过滤器匹配的所有事件"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def starknet_getNonce(self, url, **data):
        """获取与给定块中给定地址关联的随机数"""
        payload = {
            'url': url,
            'method': 'post',
            'headers': self.headers,
            'json': data
        }
        response = Utils.send_http(payload)
        return response
