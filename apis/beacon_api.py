from common.handle_path import CASE_DIR, CONFIG_DIR
from common.utils import Utils
from common.wrapper import api_call
from common.config_manager import get_config_manager


class BeaconApi:

    # 为了向后兼容，保留类级别的data属性
    _config_manager = get_config_manager()
    data = _config_manager.get_case_data()
    headers = _config_manager.get_headers()

    @api_call
    def genesis(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def block_root(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def blob_sidecars(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def block_header(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def committees(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def finality_checkpoints(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def fork(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def root(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def sync_committees(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
        
    @api_call
    def validator_balances(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def validators(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def validator_id(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def block_reward(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def validator_duties(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def beaconState_object(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def deposit_contract(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def spec(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def peer_count(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def peers(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def syncing(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def version(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
    
    @api_call
    def propose(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response

    @api_call
    def block_details(self, url, path):
        payload = {
            'url': url + path,
            'method': 'get'
        }
        response = Utils.send_http(payload)
        return response
