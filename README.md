# BlockPI RPC 测试框架

一个用于测试区块链 RPC 接口的自动化测试框架。

## 📚 项目结构

```
apis/                    # 接口层，单接口封装
common/                  # 公共方法和工具类
├── test_base.py         # 测试基类和混入类
├── enhanced_assertion_utils.py  # 增强断言工具
├── performance_monitor.py  # 性能监控器
├── test_data_manager.py    # 测试数据管理器
├── enhanced_config_manager.py  # 增强配置管理器
├── test_decorators.py      # 测试装饰器
├── utils.py             # 核心工具函数
├── config_manager.py    # 配置管理
└── ...                  # 其他工具模块
conf/                    # 配置文件
data/                    # 测试数据
log/                     # 日志文件
testcases/               # 测试用例
└── test_*.py            # 具体链的测试用例
conftest.py              # 前置条件处理
pyproject.toml           # 项目配置和pytest配置
requirements.txt         # 相关依赖包文件
```

## 🌟 核心特性

- **支持50+区块链网络**：包括Ethereum、BSC、Polygon、Arbitrum、Optimism、Solana、Starknet等
- **多环境支持**：alphanet(生产)、stage(预发布)、testnet(测试)环境
- **完整的RPC方法覆盖**：HTTP、WebSocket、Archive节点测试
- **并行测试**：支持多线程执行，提高测试效率


## 🚀 快速开始

### 环境准备

#### 安装依赖

```bash
# 安装核心依赖
pip install -r requirements.txt
```

### 运行测试

**基本运行方式**：
```bash
# 运行所有测试
pytest

# 更详细的输出
pytest -vv
```

**指定环境和链**：
```bash
# 指定环境
pytest --env alphanet

# 指定环境和链
pytest --env testnet -m bsc

# 运行多条链
pytest -m "bsc or avax"

# 排除特定链
pytest -m "evm and not bsc"

# 运行包含特定关键字的测试
pytest -k ethereum
```

**多线程运行** 🚀：
```bash
# 使用4个CPU核心并行运行
pytest -n 4

# 指定链并使用多线程
pytest -m bsc -n 4

# 自动检测CPU核心数并使用全部
pytest -n auto
```

## 🔧 配置说明
测试配置位于 conf/config.yaml 文件中，可以根据需要修改以下配置：

- 环境配置（alphanet、stage、testnet）
- 超时设置
- 重试次数
- 日志级别

## 📋 支持的区块链网络

### EVM兼容链
- Ethereum (主网/测试网)
- BSC (主网/测试网)
- Polygon (主网/测试网)
- Arbitrum (主网/Nova/测试网)
- Optimism (主网/测试网)
- Base (主网/测试网)
- Avalanche (主网/测试网)
- Fantom, Cronos, Gnosis
- zkSync Era, Polygon zkEVM
- Linea, Scroll, Mantle, Metis
- Blast, Taiko, Merlin
- 等50+网络...

### 非EVM链
- Solana
- Starknet
- TON ([详细文档](docs/test_ton_documentation.md))
- Sui
- Aptos
- Near
- Cosmos生态