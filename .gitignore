# Python virtual environment
bin/
include/
lib/
pyvenv.cfg
.Python
pip-selfcheck.json

# IDE settings
.idea/
.cursorrules
.vscode/
.kiro/

# Environment variables
.venv
.env
.env.local
.env.production
.env.staging
chain_mappings.json

# Python cache files
apis/__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
testcases/__pycache__/
.pytest_assist
__pycache__/

# Distribution / packaging
dist/
docs/
build/
*.egg-info/
OPTIMIZATION_RECOMMENDATIONS.md
API_KEY_MIGRATION_PLAN.md
.augment/rules/augment_rule.md
.env.template
scripts/validate_env_setup.py
scripts/extract_api_keys.py
CLAUDE.md
.roo/mcp.json
.claude/settings.local.json
.kilocode/rules/kilo_code.md
set_env_test.sh
