import asyncio
from aiohttp import web
import subprocess
import io
import os
import sys
import time
from datetime import datetime
import pytz
import re

# 切换为脚本路径
os.path.split(os.path.realpath(__file__))
os.chdir(os.path.abspath(os.path.dirname(sys.argv[0])))
# 设置pytest输出文件夹
output_dir = '/usr/local/cloudflareR2/blockpi/pytest/show_log/'
# 设置时区
current_timezone = pytz.timezone('Asia/Shanghai')
current_time = datetime.now(current_timezone)

def pytest_output(chain, output, error):
    filename = current_time.strftime('%Y-%m-%d-%H:%M:%S')
    file_dir = output_dir + chain
    info = '结果: \n' + output + '\n\n\n' + '错误输出: \n' + error
    # 如果目录不存在，则创建目录
    if not os.path.exists(file_dir):
        os.makedirs(file_dir)
    with open(file_dir+'/'+filename, 'w') as f:
        f.write(info)
    return f'https://filebrower.204001.xyz/api/public/dl/siVutkOU/{chain}/{filename}?inline=true'


async def check(request):
    # 获取请求参数
    chain = request.query.get('chain', 'all')
    chain = chain.replace('-', '_')
    run_env = request.query.get('env', 'alphanet')
    # 测试单链时使用单线程加快启动时间和减少CPU使用 构建pytest命令参数, 指定文件缩短采集用例时间
    pytest_command = ['pytest','-p', 'no:warnings', '-p', 'no:allure-pytest', '--env', run_env, '-m', chain, f"./testcases/test_{chain}.py"]
    if chain == 'all':
        # 跑全部是使用多线程跑
        pytest_command = ['pytest', '-p', 'no:warnings', '-p', 'no:allure-pytest', '-n', 'auto', '--env', run_env]
    print(pytest_command)
    # 运行pytest命令并捕获输出
    try:
        result = subprocess.run(pytest_command, capture_output=True, text=True, check=True)
        output = result.stdout
        error = result.stderr
    except subprocess.CalledProcessError as e:
        output = e.stdout
        error = e.stderr
    show_url = pytest_output(chain, output, error)
    re_outout1 = re.findall('short test summary info', output + error)
    re_outout2 = re.findall('file or directory not found', output + error)
    status = 'ok'
    if re_outout1 or re_outout2:
        status = 'fail fail fail'
    # 返回结果
    return web.json_response({
        'show_url': show_url,
        'status': status
    })


async def upgrade(request):
    command = ['git', 'pull']
    print(command)
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True)
        output = result.stdout
        error = result.stderr
    except subprocess.CalledProcessError as e:
        output = e.stdout
        error = e.stderr
    # 返回结果
    return web.json_response({
        'output': output,
        'error': error
    })


async def init_app():
    app = web.Application()
    app.router.add_get('/uuion/pytest', check)
    app.router.add_get('/uuion/upgrade', upgrade)
    return app


if __name__ == '__main__':
    web.run_app(init_app(), host='0.0.0.0', port=8931)


# 请求测试链 返回结果和结果链接
# http://*************:8931/uuion/pytest?chain=bsc
# 更新pytest测试用例
# http://*************:8931/uuion/upgrade

# serviceName=pytest_rpc
# serviceCmd="/usr/bin/python3 /usr/local/test_jsonrpc/pytest_rpc.py"
# Environment=PYTHONUNBUFFERED=1 显示日志
# snap2服务器
